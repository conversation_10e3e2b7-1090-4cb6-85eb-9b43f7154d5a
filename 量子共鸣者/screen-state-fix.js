/**
 * 屏幕状态自动修复脚本
 * 在页面加载时自动修复屏幕显示状态异常问题
 */

(function() {
    'use strict';

    console.log('🔧 屏幕状态自动修复脚本启动...');

    // 在DOM加载完成后立即执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoFixScreenStates);
    } else {
        autoFixScreenStates();
    }

    // 在页面完全加载后再次检查
    window.addEventListener('load', () => {
        setTimeout(autoFixScreenStates, 100);
    });

    function autoFixScreenStates() {
        console.log('🔧 开始自动修复屏幕状态...');

        // 获取所有屏幕元素和模态框
        const screens = document.querySelectorAll('.screen');
        const modals = document.querySelectorAll('.modal');
        let fixedCount = 0;

        screens.forEach((screen) => {
            const screenId = screen.id || screen.className;
            
            // 检查是否有异常的内联样式
            const hasInlineDisplay = screen.style.display && screen.style.display !== '';
            const hasInlineOpacity = screen.style.opacity && screen.style.opacity !== '';
            const hasInlineVisibility = screen.style.visibility && screen.style.visibility !== '';
            const hasActiveClass = screen.classList.contains('active');

            // 如果没有active类但有显示相关的内联样式，则清除
            if (!hasActiveClass && (hasInlineDisplay || hasInlineOpacity || hasInlineVisibility)) {
                console.log(`🔧 修复屏幕 ${screenId}: 清除异常内联样式`);
                
                // 清除内联样式，让CSS类控制
                screen.style.display = '';
                screen.style.opacity = '';
                screen.style.visibility = '';
                
                fixedCount++;
            }

            // 特别处理关卡选择屏幕
            if (screen.id === 'levelSelectScreen') {
                fixLevelSelectScreen(screen);
            }
        });

        // 修复模态框状态
        modals.forEach((modal) => {
            const modalId = modal.id || modal.className;

            // 检查模态框是否有异常的显示状态
            const hasActiveClass = modal.classList.contains('active');
            const hasInlineDisplay = modal.style.display && modal.style.display !== '' && modal.style.display !== 'none';
            const hasInlineOpacity = modal.style.opacity && modal.style.opacity !== '' && modal.style.opacity !== '0';
            const hasInlineVisibility = modal.style.visibility && modal.style.visibility !== '' && modal.style.visibility !== 'hidden';

            // 如果没有active类但有显示相关的内联样式，则清除
            if (!hasActiveClass && (hasInlineDisplay || hasInlineOpacity || hasInlineVisibility)) {
                console.log(`🔧 修复模态框 ${modalId}: 清除异常内联样式`);

                // 清除内联样式，让CSS类控制
                modal.style.display = '';
                modal.style.opacity = '';
                modal.style.visibility = '';

                fixedCount++;
            }

            // 确保模态框默认隐藏（除非有active类）
            if (!hasActiveClass) {
                modal.style.display = '';
                modal.style.opacity = '';
                modal.style.visibility = '';
            }
        });

        if (fixedCount > 0) {
            console.log(`✅ 已修复 ${fixedCount} 个元素的状态异常`);
        } else {
            console.log('✅ 所有屏幕和模态框状态正常');
        }

        // 确保只有加载屏幕在初始时显示
        ensureCorrectInitialScreen();
    }

    function fixLevelSelectScreen(screen) {
        console.log('🎯 特别处理关卡选择屏幕...');
        
        const hasActiveClass = screen.classList.contains('active');
        const computedStyle = window.getComputedStyle(screen);
        
        // 如果没有active类，确保它是隐藏的
        if (!hasActiveClass) {
            // 清除可能导致显示的内联样式
            screen.style.display = '';
            screen.style.opacity = '';
            screen.style.visibility = '';
            screen.style.position = '';
            screen.style.zIndex = '';
            
            console.log('🎯 关卡选择屏幕状态已重置为隐藏');
        }
    }

    function ensureCorrectInitialScreen() {
        console.log('📱 确保正确的初始屏幕状态...');
        
        // 在应用初始化期间，只有加载屏幕应该显示
        const loadingScreen = document.getElementById('loading-screen');
        const mainMenuScreen = document.getElementById('main-menu-screen');
        
        // 如果加载屏幕存在且有active类，其他屏幕都应该隐藏
        if (loadingScreen && loadingScreen.classList.contains('active')) {
            const otherScreens = document.querySelectorAll('.screen:not(#loading-screen)');
            otherScreens.forEach(screen => {
                if (screen.classList.contains('active')) {
                    console.log(`📱 移除 ${screen.id} 的active类（加载期间）`);
                    screen.classList.remove('active');
                }
                // 清除内联样式
                screen.style.display = '';
                screen.style.opacity = '';
                screen.style.visibility = '';
            });
        }
    }

    // 监听屏幕切换事件，确保状态正确
    document.addEventListener('click', function(e) {
        // 如果点击的是菜单按钮，延迟检查屏幕状态
        if (e.target.matches('.menu-btn, .level-back-button, [data-screen]')) {
            setTimeout(() => {
                checkAndFixAfterTransition();
            }, 100);
        }
    });

    function checkAndFixAfterTransition() {
        const screens = document.querySelectorAll('.screen');
        let activeCount = 0;
        let activeScreens = [];

        screens.forEach(screen => {
            if (screen.classList.contains('active')) {
                activeCount++;
                activeScreens.push(screen.id || screen.className);
            }
        });

        // 正常情况下应该只有一个活动屏幕
        if (activeCount > 1) {
            console.warn(`⚠️ 检测到多个活动屏幕: ${activeScreens.join(', ')}`);
            // 可以在这里添加额外的修复逻辑
        }
    }

    // 提供手动修复函数
    window.fixScreenStates = autoFixScreenStates;
    
    console.log('🔧 屏幕状态自动修复脚本加载完成');
})();
