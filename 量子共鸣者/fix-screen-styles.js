/**
 * 修复屏幕样式状态异常的脚本
 * 确保所有屏幕元素的默认状态正确
 */

class ScreenStyleFixer {
    constructor() {
        this.fixedCount = 0;
        this.checkedCount = 0;
        console.log('🔧 屏幕样式修复器已初始化');
    }

    /**
     * 修复所有屏幕的样式状态
     */
    fixAllScreenStyles() {
        console.log('🔍 开始检查和修复屏幕样式状态...');
        
        // 重置计数器
        this.fixedCount = 0;
        this.checkedCount = 0;
        
        // 修复屏幕元素
        this.fixScreenElements();
        
        // 修复模态框元素
        this.fixModalElements();
        
        // 确保正确的初始屏幕显示
        this.ensureCorrectInitialScreen();
        
        // 输出修复结果
        this.reportResults();
    }

    /**
     * 修复屏幕元素
     */
    fixScreenElements() {
        const screens = document.querySelectorAll('.screen');
        
        screens.forEach(screen => {
            this.checkedCount++;
            const screenId = screen.id || screen.className;
            
            // 检查当前状态
            const hasActiveClass = screen.classList.contains('active');
            const computedStyle = window.getComputedStyle(screen);
            const inlineOpacity = screen.style.opacity;
            const inlineVisibility = screen.style.visibility;
            const inlineDisplay = screen.style.display;
            
            console.log(`🔍 检查屏幕 ${screenId}:`, {
                hasActiveClass,
                computedOpacity: computedStyle.opacity,
                computedVisibility: computedStyle.visibility,
                inlineOpacity,
                inlineVisibility,
                inlineDisplay
            });
            
            // 如果没有active类，确保屏幕隐藏
            if (!hasActiveClass) {
                let needsFix = false;
                
                // 检查是否有异常的内联样式
                if (inlineOpacity && inlineOpacity !== '0') {
                    screen.style.opacity = '';
                    needsFix = true;
                }
                
                if (inlineVisibility && inlineVisibility !== 'hidden') {
                    screen.style.visibility = '';
                    needsFix = true;
                }
                
                if (inlineDisplay && inlineDisplay !== 'none') {
                    screen.style.display = '';
                    needsFix = true;
                }
                
                // 检查计算样式是否正确
                const newComputedStyle = window.getComputedStyle(screen);
                if (newComputedStyle.opacity !== '0' || newComputedStyle.visibility !== 'hidden') {
                    console.warn(`⚠️ 屏幕 ${screenId} 样式状态异常:`, {
                        opacity: newComputedStyle.opacity,
                        visibility: newComputedStyle.visibility
                    });
                    needsFix = true;
                }
                
                if (needsFix) {
                    console.log(`🔧 修复屏幕 ${screenId} 的样式状态`);
                    this.fixedCount++;
                }
            } else {
                // 有active类的屏幕应该可见
                const newComputedStyle = window.getComputedStyle(screen);
                if (newComputedStyle.opacity !== '1' || newComputedStyle.visibility !== 'visible') {
                    console.warn(`⚠️ 活动屏幕 ${screenId} 样式状态异常:`, {
                        opacity: newComputedStyle.opacity,
                        visibility: newComputedStyle.visibility
                    });
                }
            }
        });
    }

    /**
     * 修复模态框元素
     */
    fixModalElements() {
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            this.checkedCount++;
            const modalId = modal.id || modal.className;
            
            // 检查当前状态
            const hasActiveClass = modal.classList.contains('active');
            const inlineOpacity = modal.style.opacity;
            const inlineVisibility = modal.style.visibility;
            const inlineDisplay = modal.style.display;
            
            console.log(`🔍 检查模态框 ${modalId}:`, {
                hasActiveClass,
                inlineOpacity,
                inlineVisibility,
                inlineDisplay
            });
            
            // 如果没有active类，确保模态框隐藏
            if (!hasActiveClass) {
                let needsFix = false;
                
                if (inlineOpacity && inlineOpacity !== '0') {
                    modal.style.opacity = '';
                    needsFix = true;
                }
                
                if (inlineVisibility && inlineVisibility !== 'hidden') {
                    modal.style.visibility = '';
                    needsFix = true;
                }
                
                if (inlineDisplay && inlineDisplay !== 'none') {
                    modal.style.display = '';
                    needsFix = true;
                }
                
                if (needsFix) {
                    console.log(`🔧 修复模态框 ${modalId} 的样式状态`);
                    this.fixedCount++;
                }
            }
        });
    }

    /**
     * 确保正确的初始屏幕显示
     */
    ensureCorrectInitialScreen() {
        // 检查是否有多个屏幕同时显示
        const activeScreens = document.querySelectorAll('.screen.active');
        
        if (activeScreens.length > 1) {
            console.warn(`⚠️ 发现多个活动屏幕 (${activeScreens.length}个)，正在修复...`);
            
            // 隐藏除第一个外的所有活动屏幕
            for (let i = 1; i < activeScreens.length; i++) {
                activeScreens[i].classList.remove('active');
                console.log(`🔧 隐藏多余的活动屏幕: ${activeScreens[i].id}`);
                this.fixedCount++;
            }
        }
        
        // 如果没有活动屏幕，显示加载屏幕
        if (activeScreens.length === 0) {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.classList.add('active');
                console.log('🔧 显示默认加载屏幕');
                this.fixedCount++;
            }
        }
    }

    /**
     * 输出修复结果
     */
    reportResults() {
        console.log('\n📊 屏幕样式修复结果:');
        console.log(`   检查元素: ${this.checkedCount}个`);
        console.log(`   修复元素: ${this.fixedCount}个`);
        
        if (this.fixedCount > 0) {
            console.log('✅ 屏幕样式状态已修复');
        } else {
            console.log('✅ 所有屏幕样式状态正常');
        }
    }

    /**
     * 验证修复结果
     */
    verifyFix() {
        console.log('\n🔍 验证修复结果...');
        
        const screens = document.querySelectorAll('.screen');
        let allCorrect = true;
        
        screens.forEach(screen => {
            const screenId = screen.id || screen.className;
            const hasActiveClass = screen.classList.contains('active');
            const computedStyle = window.getComputedStyle(screen);
            
            if (!hasActiveClass) {
                // 非活动屏幕应该隐藏
                if (computedStyle.opacity !== '0' || computedStyle.visibility !== 'hidden') {
                    console.error(`❌ ${screenId} 样式状态仍然异常:`, {
                        opacity: computedStyle.opacity,
                        visibility: computedStyle.visibility
                    });
                    allCorrect = false;
                } else {
                    console.log(`✅ ${screenId} 样式状态正确`);
                }
            } else {
                // 活动屏幕应该可见
                if (computedStyle.opacity !== '1' || computedStyle.visibility !== 'visible') {
                    console.error(`❌ 活动屏幕 ${screenId} 样式状态异常:`, {
                        opacity: computedStyle.opacity,
                        visibility: computedStyle.visibility
                    });
                    allCorrect = false;
                } else {
                    console.log(`✅ 活动屏幕 ${screenId} 样式状态正确`);
                }
            }
        });
        
        return allCorrect;
    }
}

// 创建全局实例
window.screenStyleFixer = new ScreenStyleFixer();

// 添加便捷方法
window.fixScreenStyles = () => screenStyleFixer.fixAllScreenStyles();
window.verifyScreenStyles = () => screenStyleFixer.verifyFix();

// 页面加载完成后自动修复
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🔧 自动修复屏幕样式状态...');
        screenStyleFixer.fixAllScreenStyles();
        
        // 验证修复结果
        setTimeout(() => {
            screenStyleFixer.verifyFix();
        }, 500);
    }, 1000);
});

console.log('🔧 屏幕样式修复器已加载');
console.log('💡 使用 fixScreenStyles() 手动修复');
console.log('💡 使用 verifyScreenStyles() 验证状态');
