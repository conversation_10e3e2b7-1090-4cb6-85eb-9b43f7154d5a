<!DOCTYPE html>
<html>
<head>
    <title>创建基本PWA图标</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>创建基本PWA图标</h1>
    <p>此页面将自动创建缺失的PWA图标文件</p>
    
    <div id="status">正在生成图标...</div>
    <div id="downloads"></div>

    <script>
        // 创建SVG图标的base64数据
        function createSVGIcon(size, content, filename) {
            const svg = `
                <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <radialGradient id="bg" cx="50%" cy="50%" r="50%">
                            <stop offset="0%" style="stop-color:#00d4ff"/>
                            <stop offset="100%" style="stop-color:#1a1a2e"/>
                        </radialGradient>
                    </defs>
                    <circle cx="${size/2}" cy="${size/2}" r="${size/2-2}" fill="url(#bg)" stroke="#fff" stroke-width="2"/>
                    <text x="${size/2}" y="${size/2}" text-anchor="middle" dominant-baseline="middle" 
                          font-family="Arial" font-size="${size*0.3}" fill="white">${content}</text>
                </svg>
            `;
            
            // 转换SVG为PNG
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0);
                downloadCanvas(canvas, filename);
            };
            
            const svgBlob = new Blob([svg], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            img.src = url;
        }
        
        // 创建简单的PNG图标
        function createSimpleIcon(size, emoji, color, filename) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 背景圆形
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.45;
            
            // 渐变背景
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, darkenColor(color, 0.4));
            
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fillStyle = gradient;
            ctx.fill();
            
            // 边框
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 图标内容
            ctx.font = `${size * 0.35}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 3;
            ctx.fillText(emoji, centerX, centerY);
            
            downloadCanvas(canvas, filename);
        }
        
        // 创建游戏截图
        function createGameScreenshot(width, height, filename) {
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(0.5, '#16213e');
            gradient.addColorStop(1, '#0f0f1e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            const centerX = width / 2;
            const centerY = height / 2;
            const scale = Math.min(width, height) / 720;
            
            // 中心原子
            ctx.beginPath();
            ctx.arc(centerX, centerY, 20 * scale, 0, Math.PI * 2);
            const coreGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 20 * scale);
            coreGradient.addColorStop(0, '#00d4ff');
            coreGradient.addColorStop(1, '#0066cc');
            ctx.fillStyle = coreGradient;
            ctx.fill();
            
            // 轨道和粒子
            for (let i = 0; i < 3; i++) {
                const radius = (80 + i * 60) * scale;
                
                // 轨道
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.strokeStyle = `rgba(255, 107, 107, ${0.7 - i * 0.2})`;
                ctx.lineWidth = 3 * scale;
                ctx.stroke();
                
                // 粒子
                for (let j = 0; j < 4; j++) {
                    const angle = (j * Math.PI) / 2;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 8 * scale, 0, Math.PI * 2);
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fill();
                }
            }
            
            // 游戏UI
            if (width > height) {
                // 横屏UI
                ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
                ctx.fillRect(0, 0, width, 80 * scale);
                
                ctx.font = `${24 * scale}px Arial`;
                ctx.fillStyle = 'white';
                ctx.textAlign = 'left';
                ctx.fillText('分数: 12,450', 30 * scale, 50 * scale);
                
                ctx.textAlign = 'right';
                ctx.fillText('关卡 3', width - 30 * scale, 50 * scale);
            }
            
            // 标题
            ctx.font = `${32 * scale}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.textAlign = 'center';
            ctx.fillText('量子共鸣者', centerX, height - 40 * scale);
            
            downloadCanvas(canvas, filename);
        }
        
        // 颜色加深函数
        function darkenColor(color, factor) {
            const hex = color.replace('#', '');
            const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));
            const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));
            const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));
            return `rgb(${r}, ${g}, ${b})`;
        }
        
        // 下载画布
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 显示下载状态
            const downloadsDiv = document.getElementById('downloads');
            const item = document.createElement('div');
            item.textContent = `✅ 已下载: ${filename}`;
            item.style.color = 'green';
            item.style.margin = '5px 0';
            downloadsDiv.appendChild(item);
        }
        
        // 自动生成所有图标
        function generateAllAssets() {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = '正在生成图标文件...';
            
            // 快捷方式图标
            setTimeout(() => createSimpleIcon(96, '▶', '#4CAF50', 'shortcut-start.png'), 100);
            setTimeout(() => createSimpleIcon(96, '🔧', '#FF9800', 'shortcut-editor.png'), 200);
            setTimeout(() => createSimpleIcon(96, '🏆', '#FFD700', 'shortcut-achievements.png'), 300);
            
            // 截图
            setTimeout(() => createGameScreenshot(1280, 720, 'screenshot-1.png'), 400);
            setTimeout(() => createGameScreenshot(720, 1280, 'screenshot-2.png'), 500);
            
            setTimeout(() => {
                statusDiv.textContent = '✅ 所有图标已生成完成！请将下载的文件移动到 assets/images/ 目录';
                statusDiv.style.color = 'green';
                statusDiv.style.fontWeight = 'bold';
            }, 1000);
        }
        
        // 页面加载完成后自动生成
        window.onload = function() {
            generateAllAssets();
        };
    </script>
    
    <div style="margin-top: 30px; padding: 20px; background: #f0f8ff; border-radius: 8px;">
        <h3>说明：</h3>
        <p>此页面会自动下载以下文件到您的下载文件夹：</p>
        <ul>
            <li><strong>shortcut-start.png</strong> - 开始游戏快捷方式图标</li>
            <li><strong>shortcut-editor.png</strong> - 关卡编辑器快捷方式图标</li>
            <li><strong>shortcut-achievements.png</strong> - 成就系统快捷方式图标</li>
            <li><strong>screenshot-1.png</strong> - 宽屏游戏截图</li>
            <li><strong>screenshot-2.png</strong> - 窄屏游戏截图</li>
        </ul>
        <p><strong>重要：</strong>请将这些文件移动到 <code>assets/images/</code> 目录中，然后刷新游戏页面。</p>
    </div>
</body>
</html>
