<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关卡方法修复测试 - 量子共鸣者</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #2d5a2d;
            border: 1px solid #4a8a4a;
        }
        .error {
            background: #5a2d2d;
            border: 1px solid #8a4a4a;
        }
        .info {
            background: #2d4a5a;
            border: 1px solid #4a7a8a;
        }
        button {
            background: #4a7a8a;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a8a9a;
        }
        #gameCanvas {
            border: 1px solid #666;
            background: #000;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>关卡方法修复测试</h1>
        <p>测试 Level 类的 isCompleted() 和 isFailed() 方法是否正常工作</p>
        
        <div id="testResults"></div>
        
        <button onclick="runTests()">运行测试</button>
        <button onclick="startGame()">启动游戏测试</button>
        <button onclick="stopGame()">停止游戏</button>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div id="gameStatus"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/game/level.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let gameController = null;
        let testLevel = null;

        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(div);
            console.log(message);
        }

        function runTests() {
            logResult('开始运行关卡方法测试...', 'info');
            
            try {
                // 测试 Level 类是否存在
                if (typeof Level === 'undefined') {
                    logResult('❌ Level 类未定义', 'error');
                    return;
                }
                logResult('✅ Level 类已定义', 'success');

                // 创建测试关卡
                const testConfig = {
                    name: '测试关卡',
                    description: '用于测试方法的关卡',
                    targetScore: 1000,
                    timeLimit: 60,
                    particles: [
                        { x: 100, y: 100, frequency: 440, isTarget: true },
                        { x: 200, y: 200, frequency: 880, isTarget: true }
                    ]
                };

                testLevel = new Level(testConfig);
                logResult('✅ 测试关卡创建成功', 'success');

                // 测试 isCompleted() 方法
                if (typeof testLevel.isCompleted === 'function') {
                    const result = testLevel.isCompleted();
                    logResult(`✅ isCompleted() 方法存在，返回值: ${result}`, 'success');
                } else {
                    logResult('❌ isCompleted() 方法不存在', 'error');
                }

                // 测试 isFailed() 方法
                if (typeof testLevel.isFailed === 'function') {
                    const result = testLevel.isFailed();
                    logResult(`✅ isFailed() 方法存在，返回值: ${result}`, 'success');
                } else {
                    logResult('❌ isFailed() 方法不存在', 'error');
                }

                // 测试 update() 方法
                if (typeof testLevel.update === 'function') {
                    testLevel.update(16); // 模拟 16ms 的帧时间
                    logResult('✅ update() 方法存在并可调用', 'success');
                } else {
                    logResult('❌ update() 方法不存在', 'error');
                }

                // 测试兼容性方法
                if (typeof testLevel.isComplete === 'function') {
                    logResult('✅ isComplete() 方法存在（原方法）', 'success');
                }

                if (typeof testLevel.hasFailed === 'function') {
                    logResult('✅ hasFailed() 方法存在（原方法）', 'success');
                }

                logResult('所有基础方法测试完成', 'info');

            } catch (error) {
                logResult(`❌ 测试过程中发生错误: ${error.message}`, 'error');
                console.error('测试错误:', error);
            }
        }

        function startGame() {
            try {
                logResult('开始启动游戏控制器测试...', 'info');
                
                // 检查 GameController 是否存在
                if (typeof GameController === 'undefined') {
                    logResult('❌ GameController 类未定义', 'error');
                    return;
                }

                // 创建游戏控制器
                const canvas = document.getElementById('gameCanvas');
                gameController = new GameController(canvas);
                
                // 设置状态更新回调
                const originalUpdate = gameController.update.bind(gameController);
                gameController.update = function(deltaTime) {
                    try {
                        originalUpdate(deltaTime);
                        updateGameStatus();
                    } catch (error) {
                        logResult(`❌ 游戏更新错误: ${error.message}`, 'error');
                        console.error('游戏更新错误:', error);
                    }
                };

                // 初始化游戏
                gameController.initialize().then(() => {
                    logResult('✅ 游戏控制器初始化成功', 'success');
                    
                    // 启动游戏
                    gameController.startGame().then(() => {
                        logResult('✅ 游戏启动成功', 'success');
                    }).catch(error => {
                        logResult(`❌ 游戏启动失败: ${error.message}`, 'error');
                    });
                }).catch(error => {
                    logResult(`❌ 游戏控制器初始化失败: ${error.message}`, 'error');
                });

            } catch (error) {
                logResult(`❌ 启动游戏时发生错误: ${error.message}`, 'error');
                console.error('启动游戏错误:', error);
            }
        }

        function stopGame() {
            if (gameController) {
                try {
                    gameController.pauseGame();
                    logResult('✅ 游戏已暂停', 'success');
                } catch (error) {
                    logResult(`❌ 停止游戏时发生错误: ${error.message}`, 'error');
                }
            }
        }

        function updateGameStatus() {
            if (!gameController || !gameController.currentLevel) return;
            
            const statusDiv = document.getElementById('gameStatus');
            const level = gameController.currentLevel;
            
            let statusText = `
                <h3>游戏状态</h3>
                <p>关卡名称: ${level.name || '未知'}</p>
                <p>游戏状态: ${gameController.gameState}</p>
            `;
            
            if (typeof level.isCompleted === 'function') {
                statusText += `<p>关卡完成: ${level.isCompleted()}</p>`;
            }
            
            if (typeof level.isFailed === 'function') {
                statusText += `<p>关卡失败: ${level.isFailed()}</p>`;
            }
            
            if (level.elapsedTime !== undefined) {
                statusText += `<p>已用时间: ${level.elapsedTime.toFixed(1)}秒</p>`;
            }
            
            statusDiv.innerHTML = statusText;
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
