<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 空指针错误修复综合测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            color: #4fc3f7;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(79, 195, 247, 0.5);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-section h2 {
            color: #81c784;
            margin-top: 0;
        }
        .test-result {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            margin: 5px;
        }
        .status.success { background: #4caf50; }
        .status.warning { background: #ff9800; }
        .status.error { background: #f44336; }
        .button {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 量子共鸣者空指针错误修复测试</h1>
        
        <div class="test-section">
            <h2>📋 测试概览</h2>
            <p>本页面将全面测试量子共鸣者游戏中的空指针错误修复效果。</p>
            <button class="button" onclick="runComprehensiveTest()">🚀 开始综合测试</button>
            <button class="button" onclick="clearResults()">🧹 清空结果</button>
        </div>

        <div class="test-section">
            <h2>📊 测试进度</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">等待开始测试...</div>
        </div>

        <div class="test-section">
            <h2>🎯 全局对象检查</h2>
            <div class="test-result" id="globalObjectsResult">点击开始测试查看结果...</div>
        </div>

        <div class="test-section">
            <h2>⚙️ 引擎方法检查</h2>
            <div class="test-result" id="engineMethodsResult">点击开始测试查看结果...</div>
        </div>

        <div class="test-section">
            <h2>🚀 初始化状态检查</h2>
            <div class="test-result" id="initStatusResult">点击开始测试查看结果...</div>
        </div>

        <div class="test-section">
            <h2>🎮 功能测试</h2>
            <div class="test-result" id="functionalityResult">点击开始测试查看结果...</div>
        </div>

        <div class="test-section">
            <h2>📈 测试总结</h2>
            <div class="test-result" id="summaryResult">等待测试完成...</div>
        </div>
    </div>

    <!-- 加载游戏脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/game/level.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        function updateProgress(current, total, text) {
            const percentage = (current / total) * 100;
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }

        function addStatus(type, text) {
            return `<span class="status ${type}">${text}</span>`;
        }

        async function runComprehensiveTest() {
            console.log('🔍 开始综合测试...');
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            
            updateProgress(0, 100, '开始测试...');
            
            // 等待脚本加载
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 1. 全局对象检查
            updateProgress(20, 100, '检查全局对象...');
            await testGlobalObjects();
            
            // 2. 引擎方法检查
            updateProgress(40, 100, '检查引擎方法...');
            await testEngineMethods();
            
            // 3. 初始化状态检查
            updateProgress(60, 100, '检查初始化状态...');
            await testInitializationStatus();
            
            // 4. 功能测试
            updateProgress(80, 100, '进行功能测试...');
            await testFunctionality();
            
            // 5. 生成总结
            updateProgress(100, 100, '测试完成！');
            generateSummary();
        }

        async function testGlobalObjects() {
            let result = '=== 全局对象检查结果 ===\n\n';
            
            const globalObjects = [
                'InputManager', 'GameController', 'Level', 'LevelRegistry', 'GestureDetector',
                'AudioEngine', 'PhysicsEngine', 'QuantumEngine', 'RenderEngine'
            ];
            
            const globalInstances = [
                'inputManager', 'gameController', 'levelRegistry', 
                'audioEngine', 'physicsEngine', 'quantumEngine', 'renderEngine'
            ];
            
            result += '📋 全局类检查:\n';
            globalObjects.forEach(objName => {
                testResults.total++;
                const exists = typeof window[objName] !== 'undefined';
                if (exists) {
                    testResults.passed++;
                    result += `✅ ${objName}: 已定义\n`;
                } else {
                    testResults.failed++;
                    result += `❌ ${objName}: 未定义\n`;
                }
            });
            
            result += '\n🎮 全局实例检查:\n';
            globalInstances.forEach(instanceName => {
                testResults.total++;
                const exists = typeof window[instanceName] !== 'undefined';
                if (exists) {
                    testResults.passed++;
                    result += `✅ ${instanceName}: 已定义\n`;
                } else {
                    testResults.failed++;
                    result += `❌ ${instanceName}: 未定义\n`;
                }
            });
            
            document.getElementById('globalObjectsResult').textContent = result;
        }

        async function testEngineMethods() {
            let result = '=== 引擎方法检查结果 ===\n\n';
            
            const engineMethods = [
                { engine: 'audioEngine', methods: ['update', 'reset', 'clear'] },
                { engine: 'physicsEngine', methods: ['init', 'update', 'reset', 'clear'] },
                { engine: 'quantumEngine', methods: ['init', 'update', 'reset', 'clear'] },
                { engine: 'renderEngine', methods: ['update', 'reset', 'clear'] }
            ];
            
            engineMethods.forEach(({ engine, methods }) => {
                result += `⚙️ ${engine} 方法检查:\n`;
                if (typeof window[engine] !== 'undefined') {
                    methods.forEach(method => {
                        testResults.total++;
                        const exists = typeof window[engine][method] === 'function';
                        if (exists) {
                            testResults.passed++;
                            result += `  ✅ ${method}(): 已定义\n`;
                        } else {
                            testResults.failed++;
                            result += `  ❌ ${method}(): 未定义\n`;
                        }
                    });
                } else {
                    result += `  ❌ ${engine}: 引擎未定义，跳过方法检查\n`;
                }
                result += '\n';
            });
            
            document.getElementById('engineMethodsResult').textContent = result;
        }

        async function testInitializationStatus() {
            let result = '=== 初始化状态检查结果 ===\n\n';
            
            const initChecks = [
                { name: 'audioEngine.isInitialized', check: () => window.audioEngine && window.audioEngine.isInitialized },
                { name: 'physicsEngine 存在', check: () => typeof window.physicsEngine !== 'undefined' },
                { name: 'quantumEngine.isActive', check: () => window.quantumEngine && window.quantumEngine.isActive },
                { name: 'renderEngine.isInitialized', check: () => window.renderEngine && window.renderEngine.isInitialized },
                { name: 'gameController 存在', check: () => typeof window.gameController !== 'undefined' },
                { name: 'inputManager 存在', check: () => typeof window.inputManager !== 'undefined' }
            ];
            
            initChecks.forEach(({ name, check }) => {
                testResults.total++;
                try {
                    const result_check = check();
                    if (result_check) {
                        testResults.passed++;
                        result += `✅ ${name}: 正常\n`;
                    } else {
                        testResults.warnings++;
                        result += `⚠️ ${name}: 未初始化\n`;
                    }
                } catch (error) {
                    testResults.failed++;
                    result += `❌ ${name}: 检查失败 - ${error.message}\n`;
                }
            });
            
            document.getElementById('initStatusResult').textContent = result;
        }

        async function testFunctionality() {
            let result = '=== 功能测试结果 ===\n\n';
            
            // 测试引擎方法调用
            result += '🔧 引擎方法调用测试:\n';
            
            const methodTests = [
                { engine: 'audioEngine', method: 'update', args: [0.016] },
                { engine: 'physicsEngine', method: 'update', args: [0.016] },
                { engine: 'quantumEngine', method: 'update', args: [0.016] },
                { engine: 'renderEngine', method: 'update', args: [0.016] }
            ];
            
            for (const { engine, method, args } of methodTests) {
                testResults.total++;
                try {
                    if (window[engine] && typeof window[engine][method] === 'function') {
                        window[engine][method](...args);
                        testResults.passed++;
                        result += `  ✅ ${engine}.${method}(): 调用成功\n`;
                    } else {
                        testResults.warnings++;
                        result += `  ⚠️ ${engine}.${method}(): 方法不存在\n`;
                    }
                } catch (error) {
                    testResults.failed++;
                    result += `  ❌ ${engine}.${method}(): 调用失败 - ${error.message}\n`;
                }
            }
            
            // 测试Level类实例化
            result += '\n📋 Level类测试:\n';
            testResults.total++;
            try {
                const testLevel = new Level({
                    name: '测试关卡',
                    targetScore: 1000,
                    timeLimit: 60
                });
                testResults.passed++;
                result += `  ✅ Level类实例化: 成功\n`;
            } catch (error) {
                testResults.failed++;
                result += `  ❌ Level类实例化: 失败 - ${error.message}\n`;
            }
            
            document.getElementById('functionalityResult').textContent = result;
        }

        function generateSummary() {
            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            
            let result = '=== 测试总结 ===\n\n';
            result += `📊 测试统计:\n`;
            result += `总检查项: ${testResults.total}\n`;
            result += `✅ 通过: ${testResults.passed}\n`;
            result += `⚠️ 警告: ${testResults.warnings}\n`;
            result += `❌ 失败: ${testResults.failed}\n`;
            result += `成功率: ${successRate}%\n\n`;
            
            if (testResults.failed === 0) {
                result += '🎉 恭喜！所有关键功能测试通过！\n';
                result += '空指针错误修复成功！\n';
            } else if (testResults.failed <= 2) {
                result += '✅ 大部分功能正常，仍有少量问题需要解决。\n';
            } else {
                result += '⚠️ 发现多个问题，需要进一步修复。\n';
            }
            
            result += '\n修复建议:\n';
            if (testResults.failed > 0) {
                result += '- 检查失败的组件是否正确加载\n';
                result += '- 确认脚本加载顺序是否正确\n';
                result += '- 验证类定义和全局变量导出\n';
            }
            if (testResults.warnings > 0) {
                result += '- 某些组件可能需要手动初始化\n';
                result += '- 检查初始化时序和依赖关系\n';
            }
            
            document.getElementById('summaryResult').textContent = result;
        }

        function clearResults() {
            const resultElements = [
                'globalObjectsResult', 'engineMethodsResult', 
                'initStatusResult', 'functionalityResult', 'summaryResult'
            ];
            
            resultElements.forEach(id => {
                document.getElementById(id).textContent = '已清空，点击开始测试查看结果...';
            });
            
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '等待开始测试...';
        }

        // 页面加载完成后自动运行一次测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('页面加载完成，自动运行测试...');
                runComprehensiveTest();
            }, 2000);
        });
    </script>
</body>
</html>
