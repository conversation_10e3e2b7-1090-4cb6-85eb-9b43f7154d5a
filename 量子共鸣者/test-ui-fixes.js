/**
 * 量子共鸣者 - UI修复测试脚本
 * 用于测试所有修复的UI功能
 */

class UIFixesTest {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.totalTests = 0;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 开始UI修复测试...');
        
        // 等待页面加载完成
        await this.waitForPageLoad();
        
        // 测试主菜单按钮
        await this.testMainMenuButtons();
        
        // 测试设置面板功能
        await this.testSettingsPanel();
        
        // 显示测试结果
        this.showTestResults();
    }

    /**
     * 等待页面加载完成
     */
    async waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    /**
     * 测试主菜单按钮
     */
    async testMainMenuButtons() {
        console.log('🔘 测试主菜单按钮...');
        
        const buttons = [
            { id: 'start-game-btn', name: '开始游戏' },
            { id: 'level-editor-btn', name: '关卡编辑器' },
            { id: 'achievements-btn', name: '成就系统' },
            { id: 'leaderboard-btn', name: '排行榜' },
            { id: 'settings-btn', name: '设置' }
        ];

        for (const button of buttons) {
            const element = document.getElementById(button.id);
            const exists = element !== null;
            const hasClickHandler = exists && this.hasEventListener(element, 'click');
            
            this.addTestResult(
                `主菜单按钮 - ${button.name}`,
                exists && hasClickHandler,
                exists ? (hasClickHandler ? '按钮存在且有点击事件' : '按钮存在但缺少点击事件') : '按钮不存在'
            );
        }
    }

    /**
     * 测试设置面板功能
     */
    async testSettingsPanel() {
        console.log('⚙️ 测试设置面板功能...');
        
        // 测试设置面板容器
        const settingsScreen = document.getElementById('settings-screen');
        this.addTestResult(
            '设置面板容器',
            settingsScreen !== null,
            settingsScreen ? '设置面板容器存在' : '设置面板容器不存在'
        );

        if (settingsScreen) {
            // 测试滑块
            await this.testSliders();
            
            // 测试复选框
            await this.testCheckboxes();
            
            // 测试选择框
            await this.testSelects();
            
            // 测试按钮
            await this.testSettingsButtons();
        }
    }

    /**
     * 测试滑块
     */
    async testSliders() {
        const sliders = [
            { id: 'master-volume', valueId: 'master-volume-value', name: '主音量' },
            { id: 'music-volume', valueId: 'music-volume-value', name: '音乐音量' },
            { id: 'sfx-volume', valueId: 'sfx-volume-value', name: '音效音量' },
            { id: 'sensitivity', valueId: 'sensitivity-value', name: '灵敏度' }
        ];

        for (const slider of sliders) {
            const element = document.getElementById(slider.id);
            const valueElement = document.getElementById(slider.valueId);
            const exists = element !== null && valueElement !== null;
            const hasInputHandler = exists && this.hasEventListener(element, 'input');
            
            this.addTestResult(
                `滑块 - ${slider.name}`,
                exists && hasInputHandler,
                exists ? (hasInputHandler ? '滑块存在且有输入事件' : '滑块存在但缺少输入事件') : '滑块或显示值不存在'
            );
        }
    }

    /**
     * 测试复选框
     */
    async testCheckboxes() {
        const checkboxes = [
            { id: 'visual-effects', name: '视觉效果' }
        ];

        for (const checkbox of checkboxes) {
            const element = document.getElementById(checkbox.id);
            const exists = element !== null;
            const hasChangeHandler = exists && this.hasEventListener(element, 'change');
            
            this.addTestResult(
                `复选框 - ${checkbox.name}`,
                exists && hasChangeHandler,
                exists ? (hasChangeHandler ? '复选框存在且有变化事件' : '复选框存在但缺少变化事件') : '复选框不存在'
            );
        }
    }

    /**
     * 测试选择框
     */
    async testSelects() {
        const selects = [
            { id: 'particle-quality', name: '粒子质量' }
        ];

        for (const select of selects) {
            const element = document.getElementById(select.id);
            const exists = element !== null;
            const hasChangeHandler = exists && this.hasEventListener(element, 'change');
            
            this.addTestResult(
                `选择框 - ${select.name}`,
                exists && hasChangeHandler,
                exists ? (hasChangeHandler ? '选择框存在且有变化事件' : '选择框存在但缺少变化事件') : '选择框不存在'
            );
        }
    }

    /**
     * 测试设置按钮
     */
    async testSettingsButtons() {
        const buttons = [
            { id: 'save-settings-btn', name: '保存设置' },
            { id: 'reset-settings-btn', name: '重置设置' }
        ];

        for (const button of buttons) {
            const element = document.getElementById(button.id);
            const exists = element !== null;
            const hasClickHandler = exists && this.hasEventListener(element, 'click');
            
            this.addTestResult(
                `设置按钮 - ${button.name}`,
                exists && hasClickHandler,
                exists ? (hasClickHandler ? '按钮存在且有点击事件' : '按钮存在但缺少点击事件') : '按钮不存在'
            );
        }
    }

    /**
     * 检查元素是否有事件监听器
     */
    hasEventListener(element, eventType) {
        // 简单检查：尝试获取事件监听器
        // 注意：这是一个简化的检查，实际的事件监听器检测更复杂
        return element && typeof element.onclick === 'function' || 
               (element._events && element._events[eventType]) ||
               (window.settingsPanel && window.settingsPanel.isInitialized);
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, passed, message) {
        this.totalTests++;
        if (passed) {
            this.passedTests++;
        }
        
        this.testResults.push({
            name: testName,
            passed: passed,
            message: message
        });
        
        console.log(`${passed ? '✅' : '❌'} ${testName}: ${message}`);
    }

    /**
     * 显示测试结果
     */
    showTestResults() {
        console.log('\n📊 测试结果汇总:');
        console.log(`总测试数: ${this.totalTests}`);
        console.log(`通过测试: ${this.passedTests}`);
        console.log(`失败测试: ${this.totalTests - this.passedTests}`);
        console.log(`通过率: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
        
        if (this.passedTests === this.totalTests) {
            console.log('🎉 所有测试通过！');
        } else {
            console.log('⚠️ 部分测试失败，需要进一步检查');
        }
        
        // 在页面上显示结果
        this.displayResultsOnPage();
    }

    /**
     * 在页面上显示测试结果
     */
    displayResultsOnPage() {
        const resultDiv = document.createElement('div');
        resultDiv.id = 'test-results';
        resultDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
            z-index: 10000;
        `;
        
        resultDiv.innerHTML = `
            <h3>🧪 UI修复测试结果</h3>
            <p>通过: ${this.passedTests}/${this.totalTests} (${((this.passedTests / this.totalTests) * 100).toFixed(1)}%)</p>
            <button onclick="this.parentElement.remove()" style="float: right; margin-top: 10px;">关闭</button>
        `;
        
        document.body.appendChild(resultDiv);
    }
}

// 页面加载完成后自动运行测试
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM加载完成，准备运行UI测试...');
    setTimeout(() => {
        console.log('🧪 开始运行UI修复测试...');
        const tester = new UIFixesTest();
        tester.runAllTests();
    }, 2000);
});

// 导出测试类供手动使用
window.UIFixesTest = UIFixesTest;
