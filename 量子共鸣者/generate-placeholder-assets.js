/**
 * 生成PWA占位符资源的Node.js脚本
 * 使用Canvas API创建缺失的图标和截图
 */

const fs = require('fs');
const path = require('path');

// 检查是否在浏览器环境中运行
if (typeof window !== 'undefined') {
    // 浏览器环境 - 使用HTML5 Canvas
    generateAssetsInBrowser();
} else {
    console.log('请在浏览器中打开 create-missing-assets.html 来生成资源文件');
    console.log('或者手动创建以下文件到 assets/images/ 目录：');
    console.log('- shortcut-start.png (96x96)');
    console.log('- shortcut-editor.png (96x96)');
    console.log('- shortcut-achievements.png (96x96)');
    console.log('- screenshot-1.png (1280x720)');
    console.log('- screenshot-2.png (720x1280)');
}

function generateAssetsInBrowser() {
    // 创建快捷方式图标的函数
    window.createQuickAssets = function() {
        const assetsToCreate = [
            { type: 'shortcut', name: 'start', emoji: '▶️', color: '#4CAF50', size: 96 },
            { type: 'shortcut', name: 'editor', emoji: '🔧', color: '#FF9800', size: 96 },
            { type: 'shortcut', name: 'achievements', emoji: '🏆', color: '#FFD700', size: 96 }
        ];
        
        assetsToCreate.forEach(asset => {
            createIconAsset(asset);
        });
        
        // 创建截图
        createScreenshotAsset(1280, 720, '1');
        createScreenshotAsset(720, 1280, '2');
        
        console.log('所有资源文件已生成并下载');
    };
    
    function createIconAsset(config) {
        const canvas = document.createElement('canvas');
        canvas.width = config.size;
        canvas.height = config.size;
        const ctx = canvas.getContext('2d');
        
        // 背景圆形
        const centerX = config.size / 2;
        const centerY = config.size / 2;
        const radius = config.size * 0.4;
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.fillStyle = config.color;
        ctx.fill();
        
        // 图标
        ctx.font = `${config.size * 0.3}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = 'white';
        ctx.fillText(config.emoji, centerX, centerY);
        
        // 下载
        downloadCanvasAsImage(canvas, `shortcut-${config.name}.png`);
    }
    
    function createScreenshotAsset(width, height, suffix) {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        
        // 背景
        const gradient = ctx.createLinearGradient(0, 0, width, height);
        gradient.addColorStop(0, '#1a1a2e');
        gradient.addColorStop(1, '#0f0f1e');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        
        // 简单的游戏界面模拟
        const centerX = width / 2;
        const centerY = height / 2;
        
        // 中心圆
        ctx.beginPath();
        ctx.arc(centerX, centerY, Math.min(width, height) * 0.05, 0, Math.PI * 2);
        ctx.fillStyle = '#00d4ff';
        ctx.fill();
        
        // 轨道
        for (let i = 1; i <= 3; i++) {
            ctx.beginPath();
            ctx.arc(centerX, centerY, Math.min(width, height) * 0.05 * (i + 1), 0, Math.PI * 2);
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        // 标题
        ctx.font = `${Math.min(width, height) * 0.04}px Arial`;
        ctx.fillStyle = 'white';
        ctx.textAlign = 'center';
        ctx.fillText('量子共鸣者', centerX, height - 50);
        
        downloadCanvasAsImage(canvas, `screenshot-${suffix}.png`);
    }
    
    function downloadCanvasAsImage(canvas, filename) {
        const link = document.createElement('a');
        link.download = filename;
        link.href = canvas.toDataURL('image/png');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 如果在浏览器中，自动执行
if (typeof window !== 'undefined' && window.location) {
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof window.createQuickAssets === 'function') {
                    console.log('自动生成PWA资源文件...');
                    window.createQuickAssets();
                }
            }, 1000);
        });
    }
}

// 导出给Node.js使用（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateAssetsInBrowser
    };
}
