<!DOCTYPE html>
<html>
<head>
    <title>创建缺失的PWA资源</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>量子共鸣者 - 创建缺失的PWA资源</h1>
    
    <div class="section">
        <h2>快捷方式图标 (96x96)</h2>
        <canvas id="shortcut-canvas" width="96" height="96"></canvas>
        <div>
            <button onclick="createShortcutIcon('start', '▶️', '#4CAF50')">开始游戏</button>
            <button onclick="createShortcutIcon('editor', '🔧', '#FF9800')">编辑器</button>
            <button onclick="createShortcutIcon('achievements', '🏆', '#FFD700')">成就</button>
        </div>
    </div>
    
    <div class="section">
        <h2>游戏截图</h2>
        <canvas id="screenshot-canvas" width="640" height="360" style="max-width: 100%;"></canvas>
        <div>
            <button onclick="createScreenshot(1280, 720, '1')">宽屏截图 (1280x720)</button>
            <button onclick="createScreenshot(720, 1280, '2')">窄屏截图 (720x1280)</button>
        </div>
    </div>

    <div class="info">
        <h3>使用说明：</h3>
        <ol>
            <li>点击按钮创建对应的图标或截图</li>
            <li>文件会自动下载到您的下载文件夹</li>
            <li>将文件移动到 <code>assets/images/</code> 目录</li>
            <li>刷新游戏页面，PWA图标错误应该消失</li>
        </ol>
    </div>

    <script>
        // 创建快捷方式图标
        function createShortcutIcon(type, emoji, color) {
            const canvas = document.getElementById('shortcut-canvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 96, 96);
            
            // 背景圆形渐变
            const gradient = ctx.createRadialGradient(48, 48, 0, 48, 48, 45);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, darkenColor(color, 0.3));
            
            ctx.beginPath();
            ctx.arc(48, 48, 42, 0, Math.PI * 2);
            ctx.fillStyle = gradient;
            ctx.fill();
            
            // 边框
            ctx.beginPath();
            ctx.arc(48, 48, 42, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 图标符号
            ctx.font = '28px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 2;
            ctx.fillText(emoji, 48, 48);
            ctx.shadowBlur = 0;
            
            // 下载
            downloadCanvas(canvas, `shortcut-${type}.png`);
        }
        
        // 创建游戏截图
        function createScreenshot(width, height, suffix) {
            const canvas = document.getElementById('screenshot-canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(0.3, '#16213e');
            gradient.addColorStop(0.7, '#0f3460');
            gradient.addColorStop(1, '#0f0f1e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            const centerX = width / 2;
            const centerY = height / 2;
            const scale = Math.min(width, height) / 720;
            
            // 中心原子核
            ctx.beginPath();
            ctx.arc(centerX, centerY, 15 * scale, 0, Math.PI * 2);
            const coreGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 15 * scale);
            coreGradient.addColorStop(0, '#00d4ff');
            coreGradient.addColorStop(1, '#0099cc');
            ctx.fillStyle = coreGradient;
            ctx.fill();
            
            // 量子轨道和粒子
            for (let i = 0; i < 4; i++) {
                const radius = (60 + i * 40) * scale;
                const rotation = i * Math.PI / 6;
                
                // 轨道
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(rotation);
                ctx.beginPath();
                ctx.ellipse(0, 0, radius, radius * 0.3, 0, 0, Math.PI * 2);
                ctx.strokeStyle = `rgba(255, 107, 107, ${0.8 - i * 0.15})`;
                ctx.lineWidth = 2 * scale;
                ctx.stroke();
                
                // 粒子
                for (let j = 0; j < 3; j++) {
                    const angle = (j * Math.PI * 2) / 3;
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius * 0.3;
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 6 * scale, 0, Math.PI * 2);
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fill();
                }
                ctx.restore();
            }
            
            // UI元素
            if (width > height) {
                // 横屏UI
                drawHUD(ctx, width, height, scale);
            } else {
                // 竖屏UI
                drawMobileUI(ctx, width, height, scale);
            }
            
            // 标题
            ctx.font = `${24 * scale}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.textAlign = 'center';
            ctx.fillText('量子共鸣者', centerX, height - 30 * scale);
            
            downloadCanvas(canvas, `screenshot-${suffix}.png`);
        }
        
        // 绘制横屏HUD
        function drawHUD(ctx, width, height, scale) {
            // 顶部状态栏
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(0, 0, width, 60 * scale);
            
            ctx.font = `${18 * scale}px Arial`;
            ctx.fillStyle = 'white';
            ctx.textAlign = 'left';
            ctx.fillText('分数: 15,420', 20 * scale, 35 * scale);
            
            ctx.textAlign = 'right';
            ctx.fillText('关卡 5', width - 20 * scale, 35 * scale);
            
            // 能量条
            const barWidth = 200 * scale;
            const barHeight = 8 * scale;
            const barX = (width - barWidth) / 2;
            const barY = height - 40 * scale;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.fillRect(barX, barY, barWidth, barHeight);
            
            ctx.fillStyle = '#00ff88';
            ctx.fillRect(barX, barY, barWidth * 0.7, barHeight);
        }
        
        // 绘制移动端UI
        function drawMobileUI(ctx, width, height, scale) {
            // 顶部标题区
            ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
            ctx.fillRect(0, 0, width, 100 * scale);
            
            ctx.font = `${28 * scale}px Arial`;
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText('量子共鸣者', width / 2, 60 * scale);
            
            // 底部控制区
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.fillRect(0, height - 120 * scale, width, 120 * scale);
        }
        
        // 颜色加深函数
        function darkenColor(color, factor) {
            const hex = color.replace('#', '');
            const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));
            const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));
            const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));
            return `rgb(${r}, ${g}, ${b})`;
        }
        
        // 下载画布内容
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            console.log(`已下载: ${filename}`);
        }
        
        console.log('PWA资源创建工具已加载');
    </script>
</body>
</html>
