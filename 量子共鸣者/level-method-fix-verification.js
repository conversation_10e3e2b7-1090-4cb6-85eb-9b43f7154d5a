/**
 * 关卡方法修复验证脚本
 * 验证 Level 类的 isCompleted() 和 isFailed() 方法是否正常工作
 */

console.log('🔍 开始验证关卡方法修复...');

// 验证 Level 类是否存在
if (typeof Level === 'undefined') {
    console.error('❌ Level 类未定义');
} else {
    console.log('✅ Level 类已定义');
    
    // 创建测试关卡
    const testConfig = {
        name: '验证测试关卡',
        description: '用于验证方法修复的关卡',
        targetScore: 1000,
        timeLimit: 60,
        particles: [
            { x: 100, y: 100, frequency: 440, isTarget: true }
        ]
    };
    
    try {
        const testLevel = new Level(testConfig);
        console.log('✅ 测试关卡创建成功');
        
        // 验证关键方法是否存在
        const methods = [
            'update',
            'isCompleted',
            'isFailed',
            'isComplete',
            'hasFailed'
        ];
        
        methods.forEach(methodName => {
            if (typeof testLevel[methodName] === 'function') {
                console.log(`✅ ${methodName}() 方法存在`);
                
                // 测试调用方法
                try {
                    if (methodName === 'update') {
                        testLevel[methodName](16); // 传入 deltaTime
                    } else {
                        const result = testLevel[methodName]();
                        console.log(`   返回值: ${result}`);
                    }
                    console.log(`✅ ${methodName}() 方法调用成功`);
                } catch (error) {
                    console.error(`❌ ${methodName}() 方法调用失败:`, error.message);
                }
            } else {
                console.error(`❌ ${methodName}() 方法不存在`);
            }
        });
        
        // 验证方法返回值的一致性
        console.log('\n🔍 验证方法返回值一致性:');
        const isCompleted1 = testLevel.isCompleted();
        const isCompleted2 = testLevel.isComplete();
        const isFailed1 = testLevel.isFailed();
        const isFailed2 = testLevel.hasFailed();
        
        if (isCompleted1 === isCompleted2) {
            console.log('✅ isCompleted() 和 isComplete() 返回值一致');
        } else {
            console.error('❌ isCompleted() 和 isComplete() 返回值不一致');
        }
        
        if (isFailed1 === isFailed2) {
            console.log('✅ isFailed() 和 hasFailed() 返回值一致');
        } else {
            console.error('❌ isFailed() 和 hasFailed() 返回值不一致');
        }
        
        // 测试状态变化
        console.log('\n🔍 测试状态变化:');
        console.log(`初始状态 - 完成: ${testLevel.isCompleted()}, 失败: ${testLevel.isFailed()}`);
        
        // 模拟完成关卡
        testLevel.complete('测试完成');
        console.log(`完成后状态 - 完成: ${testLevel.isCompleted()}, 失败: ${testLevel.isFailed()}`);
        
        // 重置并测试失败
        testLevel.reset();
        console.log(`重置后状态 - 完成: ${testLevel.isCompleted()}, 失败: ${testLevel.isFailed()}`);
        
        testLevel.fail('测试失败');
        console.log(`失败后状态 - 完成: ${testLevel.isCompleted()}, 失败: ${testLevel.isFailed()}`);
        
        console.log('\n✅ 所有验证测试完成');
        
    } catch (error) {
        console.error('❌ 创建测试关卡失败:', error.message);
    }
}

// 验证 GameController 兼容性
if (typeof GameController !== 'undefined') {
    console.log('\n🔍 验证 GameController 兼容性...');
    
    // 检查 GameController 中使用的方法
    const gameControllerSource = GameController.toString();
    
    if (gameControllerSource.includes('isCompleted()')) {
        console.log('✅ GameController 使用 isCompleted() 方法');
    }
    
    if (gameControllerSource.includes('isFailed()')) {
        console.log('✅ GameController 使用 isFailed() 方法');
    }
    
    console.log('✅ GameController 兼容性验证完成');
} else {
    console.warn('⚠️ GameController 类未定义，跳过兼容性验证');
}

console.log('\n🎉 关卡方法修复验证完成！');
