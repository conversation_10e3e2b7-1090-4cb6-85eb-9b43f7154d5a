<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        .info { background: #2d4a5a; }
    </style>
</head>
<body>
    <h1>关卡方法修复验证</h1>
    <div id="results"></div>

    <script src="js/game/level.js"></script>
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }

        // 验证修复
        try {
            log('开始验证关卡方法修复...', 'info');
            
            if (typeof Level === 'undefined') {
                log('❌ Level 类未定义', 'error');
            } else {
                log('✅ Level 类已定义', 'success');
                
                const testLevel = new Level({
                    name: '测试关卡',
                    targetScore: 1000
                });
                
                // 测试关键方法
                const methods = ['update', 'isCompleted', 'isFailed', 'isComplete', 'hasFailed'];
                let allMethodsExist = true;
                
                methods.forEach(method => {
                    if (typeof testLevel[method] === 'function') {
                        log(`✅ ${method}() 方法存在`, 'success');
                    } else {
                        log(`❌ ${method}() 方法不存在`, 'error');
                        allMethodsExist = false;
                    }
                });
                
                if (allMethodsExist) {
                    log('🎉 所有必需的方法都存在！修复成功！', 'success');
                    
                    // 测试方法调用
                    try {
                        testLevel.update(16);
                        log('✅ update() 方法调用成功', 'success');
                        
                        const completed = testLevel.isCompleted();
                        log(`✅ isCompleted() 返回: ${completed}`, 'success');
                        
                        const failed = testLevel.isFailed();
                        log(`✅ isFailed() 返回: ${failed}`, 'success');
                        
                        log('🎉 所有方法调用成功！游戏应该可以正常运行了！', 'success');
                        
                    } catch (error) {
                        log(`❌ 方法调用失败: ${error.message}`, 'error');
                    }
                } else {
                    log('❌ 部分方法缺失，修复不完整', 'error');
                }
            }
        } catch (error) {
            log(`❌ 验证过程中发生错误: ${error.message}`, 'error');
        }
    </script>
</body>
</html>
