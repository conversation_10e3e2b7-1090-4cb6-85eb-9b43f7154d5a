/**
 * 简单的主菜单调试工具
 * 只在需要时提供基本的调试功能
 */

(function() {
    'use strict';
    
    // 只在开发模式下启用调试
    const isDebugMode = window.location.search.includes('debug=true') || 
                       window.location.hostname === 'localhost';
    
    if (!isDebugMode) return;
    
    console.log('🔧 主菜单调试工具已启用');
    
    // 全局调试函数
    window.debugMainMenu = function() {
        console.log('🔍 主菜单调试信息:');
        
        const loadingScreen = document.getElementById('loading-screen');
        const mainMenuScreen = document.getElementById('main-menu-screen');
        
        if (loadingScreen) {
            console.log('📱 加载屏幕:', {
                hasActiveClass: loadingScreen.classList.contains('active'),
                computedDisplay: getComputedStyle(loadingScreen).display,
                computedVisibility: getComputedStyle(loadingScreen).visibility,
                computedOpacity: getComputedStyle(loadingScreen).opacity
            });
        }
        
        if (mainMenuScreen) {
            console.log('🏠 主菜单屏幕:', {
                hasActiveClass: mainMenuScreen.classList.contains('active'),
                computedDisplay: getComputedStyle(mainMenuScreen).display,
                computedVisibility: getComputedStyle(mainMenuScreen).visibility,
                computedOpacity: getComputedStyle(mainMenuScreen).opacity
            });
        }
        
        if (window.quantumApp) {
            console.log('📱 应用状态:', {
                appState: window.quantumApp.appState,
                isInitialized: window.quantumApp.isInitialized,
                isLoading: window.quantumApp.isLoading
            });
        }
    };
    
    // 简单的修复函数
    window.fixMainMenu = function() {
        console.log('🔧 尝试修复主菜单显示...');
        
        const loadingScreen = document.getElementById('loading-screen');
        const mainMenuScreen = document.getElementById('main-menu-screen');
        
        // 隐藏加载屏幕
        if (loadingScreen && loadingScreen.classList.contains('active')) {
            loadingScreen.classList.remove('active');
            console.log('✅ 已隐藏加载屏幕');
        }
        
        // 显示主菜单
        if (mainMenuScreen && !mainMenuScreen.classList.contains('active')) {
            mainMenuScreen.classList.add('active');
            console.log('✅ 已显示主菜单');
        }
    };
    
    // 在控制台提示可用的调试命令
    console.log('💡 可用的调试命令:');
    console.log('  debugMainMenu() - 查看主菜单状态');
    console.log('  fixMainMenu() - 尝试修复主菜单显示');
    
})();
