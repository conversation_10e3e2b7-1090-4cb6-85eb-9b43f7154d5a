/**
 * 调试验证脚本 - 验证空指针错误修复效果
 */

console.log('🔍 开始验证空指针错误修复...');

// 等待页面完全加载
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('\n=== 空指针错误修复验证报告 ===');
        
        // 验证全局对象
        const globalObjects = [
            'InputManager',
            'GameController', 
            'Level',
            'LevelRegistry',
            'GestureDetector',
            'AudioEngine',
            'PhysicsEngine',
            'QuantumEngine',
            'RenderEngine'
        ];
        
        console.log('\n📋 全局类检查:');
        globalObjects.forEach(objName => {
            const exists = typeof window[objName] !== 'undefined';
            console.log(`${exists ? '✅' : '❌'} ${objName}: ${exists ? '已定义' : '未定义'}`);
        });
        
        // 验证全局实例
        const globalInstances = [
            'inputManager',
            'gameController',
            'levelRegistry', 
            'audioEngine',
            'physicsEngine',
            'quantumEngine',
            'renderEngine'
        ];
        
        console.log('\n🎮 全局实例检查:');
        globalInstances.forEach(instanceName => {
            const exists = typeof window[instanceName] !== 'undefined';
            console.log(`${exists ? '✅' : '❌'} ${instanceName}: ${exists ? '已定义' : '未定义'}`);
        });
        
        // 验证引擎方法
        console.log('\n⚙️ 引擎方法检查:');
        const engineMethods = [
            { engine: 'audioEngine', methods: ['update', 'reset', 'clear'] },
            { engine: 'physicsEngine', methods: ['init', 'update', 'reset', 'clear'] },
            { engine: 'quantumEngine', methods: ['init', 'update', 'reset', 'clear'] },
            { engine: 'renderEngine', methods: ['update', 'reset', 'clear'] }
        ];
        
        engineMethods.forEach(({ engine, methods }) => {
            if (typeof window[engine] !== 'undefined') {
                methods.forEach(method => {
                    const exists = typeof window[engine][method] === 'function';
                    console.log(`${exists ? '✅' : '❌'} ${engine}.${method}(): ${exists ? '已定义' : '未定义'}`);
                });
            } else {
                console.log(`❌ ${engine}: 引擎未定义，跳过方法检查`);
            }
        });
        
        // 验证初始化状态
        console.log('\n🚀 初始化状态检查:');
        const initChecks = [
            { name: 'audioEngine.isInitialized', check: () => window.audioEngine && window.audioEngine.isInitialized },
            { name: 'physicsEngine 存在', check: () => typeof window.physicsEngine !== 'undefined' },
            { name: 'quantumEngine.isActive', check: () => window.quantumEngine && window.quantumEngine.isActive },
            { name: 'renderEngine.isInitialized', check: () => window.renderEngine && window.renderEngine.isInitialized }
        ];
        
        initChecks.forEach(({ name, check }) => {
            try {
                const result = check();
                console.log(`${result ? '✅' : '⚠️'} ${name}: ${result ? '正常' : '未初始化'}`);
            } catch (error) {
                console.log(`❌ ${name}: 检查失败 - ${error.message}`);
            }
        });
        
        // 统计修复结果
        let totalChecks = 0;
        let passedChecks = 0;
        
        // 统计全局对象
        globalObjects.forEach(objName => {
            totalChecks++;
            if (typeof window[objName] !== 'undefined') passedChecks++;
        });
        
        // 统计全局实例
        globalInstances.forEach(instanceName => {
            totalChecks++;
            if (typeof window[instanceName] !== 'undefined') passedChecks++;
        });
        
        // 统计引擎方法
        engineMethods.forEach(({ engine, methods }) => {
            if (typeof window[engine] !== 'undefined') {
                methods.forEach(method => {
                    totalChecks++;
                    if (typeof window[engine][method] === 'function') passedChecks++;
                });
            }
        });
        
        console.log('\n📊 修复效果统计:');
        console.log(`总检查项: ${totalChecks}`);
        console.log(`通过检查: ${passedChecks}`);
        console.log(`修复成功率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);
        
        if (passedChecks === totalChecks) {
            console.log('🎉 所有空指针错误已修复！');
        } else {
            console.log(`⚠️ 还有 ${totalChecks - passedChecks} 个问题需要解决`);
        }
        
        console.log('\n=== 验证完成 ===\n');
        
    }, 2000); // 等待2秒确保所有脚本加载完成
});
