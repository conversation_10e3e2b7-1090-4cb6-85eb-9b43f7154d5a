<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主菜单测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #0d1421;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .test-button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #3a8eef;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .status.success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid green;
        }
        
        .status.error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid red;
        }
        
        .status.warning {
            background: rgba(255, 255, 0, 0.2);
            border: 1px solid orange;
        }
        
        #test-results {
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            background: #000;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>量子共鸣者 - 主菜单显示测试</h1>
        
        <div class="test-section">
            <h2>快速测试</h2>
            <button class="test-button" onclick="openMainGame()">打开主游戏</button>
            <button class="test-button" onclick="testMainMenuElements()">测试主菜单元素</button>
            <button class="test-button" onclick="runFullTest()">运行完整测试</button>
        </div>
        
        <div class="test-section">
            <h2>调试工具</h2>
            <button class="test-button" onclick="debugScreens()">调试屏幕状态</button>
            <button class="test-button" onclick="forceShowMenu()">强制显示主菜单</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="test-section">
            <h2>测试结果</h2>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        let testResults = document.getElementById('test-results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            testResults.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            testResults.scrollTop = testResults.scrollHeight;
        }
        
        function openMainGame() {
            log('打开主游戏页面...');
            window.open('index.html', '_blank');
        }
        
        function testMainMenuElements() {
            log('开始测试主菜单元素...');
            
            // 在新窗口中测试
            const testWindow = window.open('index.html', 'test-window');
            
            setTimeout(() => {
                try {
                    const doc = testWindow.document;
                    
                    // 检查关键元素
                    const elements = {
                        'loading-screen': doc.getElementById('loading-screen'),
                        'main-menu-screen': doc.getElementById('main-menu-screen'),
                        'menu-background-canvas': doc.getElementById('menu-background-canvas'),
                        'start-game-btn': doc.getElementById('start-game-btn')
                    };
                    
                    Object.entries(elements).forEach(([name, element]) => {
                        if (element) {
                            const styles = testWindow.getComputedStyle(element);
                            log(`${name}: 存在 (display: ${styles.display}, opacity: ${styles.opacity})`, 'success');
                        } else {
                            log(`${name}: 不存在`, 'error');
                        }
                    });
                    
                    // 检查修复脚本
                    if (testWindow.forceShowMainMenu) {
                        log('修复脚本已加载', 'success');
                    } else {
                        log('修复脚本未加载', 'warning');
                    }
                    
                } catch (error) {
                    log(`测试失败: ${error.message}`, 'error');
                }
            }, 3000);
        }
        
        function runFullTest() {
            log('开始运行完整测试...');
            
            const testWindow = window.open('index.html', 'full-test-window');
            
            let checkCount = 0;
            const maxChecks = 10;
            
            const checkInterval = setInterval(() => {
                checkCount++;
                
                try {
                    const doc = testWindow.document;
                    const loadingScreen = doc.getElementById('loading-screen');
                    const mainMenuScreen = doc.getElementById('main-menu-screen');
                    
                    log(`检查 ${checkCount}/${maxChecks}:`);
                    
                    if (loadingScreen) {
                        const loadingVisible = testWindow.getComputedStyle(loadingScreen).display !== 'none';
                        log(`  加载屏幕: ${loadingVisible ? '显示' : '隐藏'}`);
                    }
                    
                    if (mainMenuScreen) {
                        const menuVisible = testWindow.getComputedStyle(mainMenuScreen).display !== 'none';
                        const hasActive = mainMenuScreen.classList.contains('active');
                        log(`  主菜单: ${menuVisible ? '显示' : '隐藏'}, active类: ${hasActive}`);
                        
                        if (menuVisible && hasActive) {
                            log('✅ 主菜单正常显示！', 'success');
                            clearInterval(checkInterval);
                            return;
                        }
                    }
                    
                    if (checkCount >= maxChecks) {
                        log('测试完成，主菜单可能存在显示问题', 'warning');
                        clearInterval(checkInterval);
                    }
                    
                } catch (error) {
                    log(`检查失败: ${error.message}`, 'error');
                    clearInterval(checkInterval);
                }
            }, 1000);
        }
        
        function debugScreens() {
            log('调试屏幕状态...');
            
            const testWindow = window.open('index.html', 'debug-window');
            
            setTimeout(() => {
                try {
                    if (testWindow.debugScreens) {
                        testWindow.debugScreens();
                        log('调试信息已输出到控制台', 'success');
                    } else {
                        log('调试函数不可用', 'warning');
                    }
                } catch (error) {
                    log(`调试失败: ${error.message}`, 'error');
                }
            }, 2000);
        }
        
        function forceShowMenu() {
            log('强制显示主菜单...');
            
            const testWindow = window.open('index.html', 'force-window');
            
            setTimeout(() => {
                try {
                    if (testWindow.forceFixMainMenu) {
                        testWindow.forceFixMainMenu();
                        log('已执行强制修复', 'success');
                    } else {
                        log('强制修复函数不可用', 'warning');
                    }
                } catch (error) {
                    log(`强制修复失败: ${error.message}`, 'error');
                }
            }, 2000);
        }
        
        function clearResults() {
            testResults.textContent = '';
            log('测试结果已清空');
        }
        
        // 初始化
        log('主菜单测试工具已加载');
        log('点击上方按钮开始测试');
    </script>
</body>
</html>
