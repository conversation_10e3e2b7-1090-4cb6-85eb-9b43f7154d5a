/**
 * 量子共鸣者 - 渲染引擎
 * 负责2D/3D图形渲染、粒子效果、视觉特效
 */

class RenderEngine {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.gl = null; // WebGL上下文
        this.width = 0;
        this.height = 0;
        this.pixelRatio = window.devicePixelRatio || 1;

        // 渲染模式
        this.renderMode = '2d'; // '2d' 或 '3d'
        this.webglSupported = false;

        // 渲染设置
        this.backgroundColor = '#0a0a0f';
        this.showDebugInfo = false;
        this.enableParticleTrails = true;
        this.enableGlowEffects = true;
        this.maxParticleTrails = 50;
        this.enable3D = true;
        this.sceneManager3D = null; // 3D场景管理器

        // 相机设置
        this.camera = {
            x: 0,
            y: 0,
            z: 0,
            zoom: 1.0,
            rotation: 0
        };
        
        // 粒子轨迹
        this.particleTrails = new Map();
        
        // 渲染缓存
        this.particleCache = new Map();
        this.effectCache = new Map();
        
        // 性能监控
        this.frameCount = 0;
        this.fps = 60;
        this.lastFrameTime = 0;
        this.renderTime = 0;
        
        console.log('🎨 渲染引擎已创建');
    }

    /**
     * 初始化渲染引擎
     * @param {HTMLCanvasElement} canvas - Canvas元素
     */
    async init(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');

        if (!this.ctx) {
            console.error('❌ 无法获取2D渲染上下文');
            return false;
        }

        // 检测WebGL支持
        this.webglSupported = this.checkWebGLSupport();

        // 初始化3D渲染
        if (this.enable3D && this.webglSupported) {
            await this.init3D();
        }

        // 设置画布大小
        this.resize();

        // 设置渲染质量
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';

        // 监听窗口大小变化
        window.addEventListener('resize', () => this.resize());

        console.log('🎨 渲染引擎初始化成功');
        return true;
    }

    /**
     * 检测WebGL支持
     * @returns {boolean} 是否支持WebGL
     */
    checkWebGLSupport() {
        try {
            const testCanvas = document.createElement('canvas');
            const gl = testCanvas.getContext('webgl2') || testCanvas.getContext('webgl');
            return !!gl;
        } catch (e) {
            return false;
        }
    }

    /**
     * 初始化3D渲染
     */
    async init3D() {
        try {
            // 创建3D场景管理器
            if (window.SceneManager3D) {
                this.sceneManager3D = new SceneManager3D();
                const success = await this.sceneManager3D.init(this.canvas);

                if (success) {
                    this.renderMode = '3d';
                    console.log('✅ 3D渲染系统初始化成功');
                } else {
                    console.warn('⚠️ 3D渲染系统初始化失败，回退到2D模式');
                    this.renderMode = '2d';
                }
            } else {
                console.warn('⚠️ 3D场景管理器未加载，使用2D模式');
                this.renderMode = '2d';
            }
        } catch (error) {
            console.error('❌ 3D渲染初始化错误:', error);
            this.renderMode = '2d';
        }
    }

    /**
     * 调整画布大小
     */
    resize() {
        if (!this.canvas) return;
        
        const rect = this.canvas.parentElement.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;
        
        // 设置画布实际大小（考虑设备像素比）
        this.canvas.width = this.width * this.pixelRatio;
        this.canvas.height = this.height * this.pixelRatio;
        
        // 设置画布显示大小
        this.canvas.style.width = this.width + 'px';
        this.canvas.style.height = this.height + 'px';
        
        // 缩放上下文以匹配设备像素比
        this.ctx.scale(this.pixelRatio, this.pixelRatio);
        
        // 更新相机中心
        this.camera.x = this.width / 2;
        this.camera.y = this.height / 2;
    }

    /**
     * 渲染一帧
     * @param {number} deltaTime - 时间增量
     */
    render(deltaTime) {
        const startTime = performance.now();

        // 根据渲染模式选择渲染方法
        if (this.renderMode === '3d' && this.sceneManager3D) {
            this.render3D(deltaTime);
        } else {
            this.render2D(deltaTime);
        }

        // 更新性能统计
        this.updatePerformanceStats(startTime);
    }

    /**
     * 2D渲染
     * @param {number} deltaTime - 时间增量
     */
    render2D(deltaTime) {
        // 清空画布
        this.clear();

        // 保存上下文状态
        this.ctx.save();

        // 应用相机变换
        this.applyCamera();

        // 渲染背景效果
        this.renderBackground();

        // 渲染量子场
        this.renderQuantumField();

        // 渲染能量波
        this.renderEnergyWaves();

        // 渲染粒子轨迹
        if (this.enableParticleTrails) {
            this.renderParticleTrails();
        }

        // 渲染粒子
        this.renderParticles();

        // 渲染连接线
        this.renderConnections();

        // 渲染特效
        this.renderEffects();

        // 恢复上下文状态
        this.ctx.restore();

        // 渲染UI元素（不受相机影响）
        this.renderUI();
    }

    /**
     * 3D渲染
     * @param {number} deltaTime - 时间增量
     */
    render3D(deltaTime) {
        // 更新3D场景
        this.sceneManager3D.update(deltaTime);

        // 渲染3D场景
        this.sceneManager3D.render();

        // 在3D场景上叠加2D UI元素
        this.renderUI();
        
        // 渲染调试信息
        if (this.showDebugInfo) {
            this.renderDebugInfo();
        }
    }

    /**
     * 清空画布
     */
    clear() {
        this.ctx.fillStyle = this.backgroundColor;
        this.ctx.fillRect(0, 0, this.width, this.height);
    }

    /**
     * 更新渲染引擎
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.isInitialized) return;

        // 更新粒子系统
        if (this.particleSystem) {
            this.particleSystem.update(deltaTime);
        }

        // 更新相机
        if (this.camera) {
            // 相机平滑移动等更新逻辑
            this.updateCamera(deltaTime);
        }

        // 更新后处理效果
        this.updatePostProcessing(deltaTime);
    }

    /**
     * 重置渲染引擎
     */
    reset() {
        if (!this.isInitialized) return;

        // 重置相机
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1,
            rotation: 0
        };

        // 重置粒子系统
        if (this.particleSystem) {
            this.particleSystem.reset();
        }

        // 清空画布
        this.clear();

        console.log('🎨 渲染引擎已重置');
    }

    /**
     * 更新相机
     * @param {number} deltaTime - 时间增量
     */
    updateCamera(deltaTime) {
        // 相机更新逻辑（如果需要的话）
    }

    /**
     * 更新后处理效果
     * @param {number} deltaTime - 时间增量
     */
    updatePostProcessing(deltaTime) {
        // 后处理效果更新逻辑
    }

    /**
     * 应用相机变换
     */
    applyCamera() {
        this.ctx.translate(this.camera.x, this.camera.y);
        this.ctx.scale(this.camera.zoom, this.camera.zoom);
        this.ctx.rotate(this.camera.rotation);
        this.ctx.translate(-this.camera.x, -this.camera.y);
    }

    /**
     * 渲染背景效果
     */
    renderBackground() {
        // 渲染星空背景
        this.ctx.fillStyle = '#ffffff';
        for (let i = 0; i < 100; i++) {
            const x = (i * 137.5) % this.width;
            const y = (i * 73.3) % this.height;
            const size = Math.sin(i) * 0.5 + 0.5;
            
            this.ctx.globalAlpha = size * 0.3;
            this.ctx.fillRect(x, y, 1, 1);
        }
        this.ctx.globalAlpha = 1;
        
        // 渲染量子网格
        this.renderQuantumGrid();
    }

    /**
     * 渲染量子网格
     */
    renderQuantumGrid() {
        const gridSize = 50;
        const fieldStrength = quantumEngine.fieldStrength;
        
        this.ctx.strokeStyle = `rgba(108, 92, 231, ${0.1 * fieldStrength})`;
        this.ctx.lineWidth = 0.5;
        this.ctx.setLineDash([2, 4]);
        
        this.ctx.beginPath();
        
        // 垂直线
        for (let x = 0; x < this.width; x += gridSize) {
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.height);
        }
        
        // 水平线
        for (let y = 0; y < this.height; y += gridSize) {
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.width, y);
        }
        
        this.ctx.stroke();
        this.ctx.setLineDash([]);
    }

    /**
     * 渲染量子场
     */
    renderQuantumField() {
        quantumEngine.quantumField.forEach((field, key) => {
            const [gridX, gridY] = key.split(',').map(Number);
            const x = gridX * 50 + 25;
            const y = gridY * 50 + 25;
            
            const intensity = Math.min(1, field.energy * 0.1);
            if (intensity < 0.1) return;
            
            // 渲染场强度
            const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, 25);
            gradient.addColorStop(0, `rgba(108, 92, 231, ${intensity * 0.3})`);
            gradient.addColorStop(1, 'rgba(108, 92, 231, 0)');
            
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(x - 25, y - 25, 50, 50);
        });
    }

    /**
     * 渲染能量波
     */
    renderEnergyWaves() {
        quantumEngine.energyWaves.forEach(wave => {
            if (!wave.isActive) return;
            
            const alpha = wave.strength * (1 - wave.age / wave.maxAge);
            if (alpha < 0.05) return;
            
            // 渲染波环
            this.ctx.strokeStyle = `rgba(116, 185, 255, ${alpha})`;
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([]);
            
            this.ctx.beginPath();
            this.ctx.arc(wave.x, wave.y, wave.radius, 0, Math.PI * 2);
            this.ctx.stroke();
            
            // 渲染内部光晕
            if (this.enableGlowEffects) {
                const gradient = this.ctx.createRadialGradient(
                    wave.x, wave.y, 0,
                    wave.x, wave.y, wave.radius
                );
                gradient.addColorStop(0, `rgba(116, 185, 255, ${alpha * 0.1})`);
                gradient.addColorStop(1, 'rgba(116, 185, 255, 0)');
                
                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(wave.x, wave.y, wave.radius, 0, Math.PI * 2);
                this.ctx.fill();
            }
        });
    }

    /**
     * 渲染粒子轨迹
     */
    renderParticleTrails() {
        physicsEngine.particles.forEach(particle => {
            if (!this.particleTrails.has(particle.id)) {
                this.particleTrails.set(particle.id, []);
            }
            
            const trail = this.particleTrails.get(particle.id);
            
            // 添加当前位置到轨迹
            trail.push({ x: particle.x, y: particle.y, time: Date.now() });
            
            // 限制轨迹长度
            if (trail.length > this.maxParticleTrails) {
                trail.shift();
            }
            
            // 清理过期轨迹点
            const now = Date.now();
            while (trail.length > 0 && now - trail[0].time > 2000) {
                trail.shift();
            }
            
            // 渲染轨迹
            if (trail.length > 1) {
                this.ctx.strokeStyle = particle.color;
                this.ctx.lineWidth = 1;
                this.ctx.globalAlpha = 0.3;
                
                this.ctx.beginPath();
                this.ctx.moveTo(trail[0].x, trail[0].y);
                
                for (let i = 1; i < trail.length; i++) {
                    this.ctx.lineTo(trail[i].x, trail[i].y);
                }
                
                this.ctx.stroke();
                this.ctx.globalAlpha = 1;
            }
        });
    }

    /**
     * 渲染粒子
     */
    renderParticles() {
        physicsEngine.particles.forEach(particle => {
            this.renderParticle(particle);
        });
    }

    /**
     * 渲染单个粒子
     * @param {Object} particle - 粒子对象
     */
    renderParticle(particle) {
        const size = particle.radius * particle.scale;
        const alpha = particle.opacity;
        
        if (alpha < 0.01) return;
        
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        
        // 移动到粒子位置
        this.ctx.translate(particle.x, particle.y);
        this.ctx.rotate(particle.rotation);
        
        // 渲染粒子核心
        if (particle.isActive) {
            // 激活状态 - 发光效果
            if (this.enableGlowEffects) {
                const glowSize = size * (1 + particle.resonanceStrength);
                const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, glowSize);
                gradient.addColorStop(0, particle.color);
                gradient.addColorStop(0.7, particle.color + '80');
                gradient.addColorStop(1, 'transparent');
                
                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(0, 0, glowSize, 0, Math.PI * 2);
                this.ctx.fill();
            }
            
            // 核心
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, size, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 共鸣环
            if (particle.resonanceStrength > 0.3) {
                this.ctx.strokeStyle = particle.color;
                this.ctx.lineWidth = 2;
                this.ctx.globalAlpha = particle.resonanceStrength;
                
                this.ctx.beginPath();
                this.ctx.arc(0, 0, size * 1.5, 0, Math.PI * 2);
                this.ctx.stroke();
            }
        } else {
            // 未激活状态
            this.ctx.fillStyle = particle.color + '60';
            this.ctx.strokeStyle = particle.color;
            this.ctx.lineWidth = 1;
            
            this.ctx.beginPath();
            this.ctx.arc(0, 0, size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.stroke();
        }
        
        this.ctx.restore();
    }

    /**
     * 渲染连接线
     */
    renderConnections() {
        physicsEngine.particles.forEach(particle => {
            if (!particle.isActive || particle.connections.length === 0) return;
            
            particle.connections.forEach(connId => {
                const connParticle = physicsEngine.getParticle(connId);
                if (!connParticle || !connParticle.isActive) return;
                
                // 避免重复渲染连接
                if (particle.id > connId) return;
                
                const resonance = MathUtils.calculateResonance(
                    particle.frequency, 
                    connParticle.frequency, 
                    50
                );
                
                if (resonance < 0.3) return;
                
                // 渲染连接线
                this.ctx.strokeStyle = `rgba(116, 185, 255, ${resonance * 0.8})`;
                this.ctx.lineWidth = resonance * 3;
                this.ctx.setLineDash([]);
                
                this.ctx.beginPath();
                this.ctx.moveTo(particle.x, particle.y);
                this.ctx.lineTo(connParticle.x, connParticle.y);
                this.ctx.stroke();
                
                // 渲染能量流动效果
                const distance = MathUtils.distance(
                    particle.x, particle.y, 
                    connParticle.x, connParticle.y
                );
                const flowSpeed = 100; // 像素/秒
                const flowPosition = (Date.now() / 1000 * flowSpeed) % distance;
                
                const t = flowPosition / distance;
                const flowX = MathUtils.lerp(particle.x, connParticle.x, t);
                const flowY = MathUtils.lerp(particle.y, connParticle.y, t);
                
                this.ctx.fillStyle = `rgba(255, 255, 255, ${resonance})`;
                this.ctx.beginPath();
                this.ctx.arc(flowX, flowY, 2, 0, Math.PI * 2);
                this.ctx.fill();
            });
        });
    }

    /**
     * 渲染特效
     */
    renderEffects() {
        // 这里可以添加各种特效渲染
        // 例如：爆炸效果、闪光效果等
    }

    /**
     * 渲染UI元素
     */
    renderUI() {
        // 渲染频率指示器
        this.renderFrequencyIndicator();
        
        // 渲染分数和连击
        this.renderScoreInfo();
    }

    /**
     * 渲染频率指示器
     */
    renderFrequencyIndicator() {
        const targetFreq = quantumEngine.targetFrequency;
        const x = this.width - 200;
        const y = 50;
        const width = 150;
        const height = 20;
        
        // 背景
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(x, y, width, height);
        
        // 频率条
        const freqRatio = (targetFreq - quantumEngine.minFrequency) / 
                         (quantumEngine.maxFrequency - quantumEngine.minFrequency);
        const barWidth = width * freqRatio;
        
        this.ctx.fillStyle = '#6c5ce7';
        this.ctx.fillRect(x, y, barWidth, height);
        
        // 边框
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(x, y, width, height);
        
        // 频率文本
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${targetFreq.toFixed(0)} Hz`, x + width / 2, y + height + 15);
    }

    /**
     * 渲染分数信息
     */
    renderScoreInfo() {
        const stats = quantumEngine.getStats();
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'left';
        
        this.ctx.fillText(`分数: ${stats.score}`, 20, 30);
        this.ctx.fillText(`连击: ${stats.combo}`, 20, 50);
        this.ctx.fillText(`关卡: ${stats.level}`, 20, 70);
    }

    /**
     * 渲染调试信息
     */
    renderDebugInfo() {
        const stats = quantumEngine.getStats();
        const physicsStats = physicsEngine.getStats();
        
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(10, this.height - 120, 200, 110);
        
        this.ctx.fillStyle = '#00ff00';
        this.ctx.font = '12px monospace';
        this.ctx.textAlign = 'left';
        
        const debugInfo = [
            `FPS: ${this.fps.toFixed(1)}`,
            `渲染时间: ${this.renderTime.toFixed(2)}ms`,
            `粒子数: ${physicsStats.particleCount}`,
            `能量波: ${stats.activeWaves}`,
            `连锁反应: ${stats.activeChains}`,
            `场强度: ${stats.fieldStrength.toFixed(2)}`,
            `相机缩放: ${this.camera.zoom.toFixed(2)}`
        ];
        
        debugInfo.forEach((info, index) => {
            this.ctx.fillText(info, 15, this.height - 100 + index * 15);
        });
    }

    /**
     * 更新性能统计
     * @param {number} startTime - 开始时间
     */
    updatePerformanceStats(startTime) {
        this.renderTime = performance.now() - startTime;
        this.frameCount++;
        
        const now = performance.now();
        if (now - this.lastFrameTime >= 1000) {
            this.fps = this.frameCount * 1000 / (now - this.lastFrameTime);
            this.frameCount = 0;
            this.lastFrameTime = now;
        }
    }

    /**
     * 设置相机位置
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    setCameraPosition(x, y) {
        this.camera.x = x;
        this.camera.y = y;
    }

    /**
     * 设置相机缩放
     * @param {number} zoom - 缩放级别
     */
    setCameraZoom(zoom) {
        this.camera.zoom = MathUtils.clamp(zoom, 0.1, 5.0);
    }

    /**
     * 屏幕坐标转世界坐标
     * @param {number} screenX - 屏幕X坐标
     * @param {number} screenY - 屏幕Y坐标
     * @returns {Object} 世界坐标 {x, y}
     */
    screenToWorld(screenX, screenY) {
        const worldX = (screenX - this.camera.x) / this.camera.zoom + this.camera.x;
        const worldY = (screenY - this.camera.y) / this.camera.zoom + this.camera.y;
        return { x: worldX, y: worldY };
    }

    /**
     * 世界坐标转屏幕坐标
     * @param {number} worldX - 世界X坐标
     * @param {number} worldY - 世界Y坐标
     * @returns {Object} 屏幕坐标 {x, y}
     */
    worldToScreen(worldX, worldY) {
        const screenX = (worldX - this.camera.x) * this.camera.zoom + this.camera.x;
        const screenY = (worldY - this.camera.y) * this.camera.zoom + this.camera.y;
        return { x: screenX, y: screenY };
    }

    /**
     * 切换渲染模式
     * @param {string} mode - 渲染模式 ('2d' 或 '3d')
     */
    setRenderMode(mode) {
        if (mode === '3d' && !this.webglSupported) {
            console.warn('⚠️ WebGL不受支持，无法切换到3D模式');
            return false;
        }

        if (mode === '3d' && !this.sceneManager3D) {
            console.warn('⚠️ 3D场景管理器未初始化');
            return false;
        }

        this.renderMode = mode;
        console.log(`🎨 渲染模式已切换到: ${mode}`);
        return true;
    }

    /**
     * 获取当前渲染模式
     * @returns {string} 当前渲染模式
     */
    getRenderMode() {
        return this.renderMode;
    }

    /**
     * 获取3D场景管理器
     * @returns {SceneManager3D|null} 3D场景管理器
     */
    getSceneManager3D() {
        return this.sceneManager3D;
    }

    /**
     * 激活3D粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} frequency - 频率
     */
    activateParticle3D(x, y, frequency) {
        if (this.sceneManager3D && this.sceneManager3D.particleSystem) {
            this.sceneManager3D.particleSystem.activateParticle(x, y, frequency);
        }
    }

    /**
     * 获取渲染统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const baseStats = {
            fps: this.fps,
            renderTime: this.renderTime,
            frameCount: this.frameCount,
            renderMode: this.renderMode,
            webglSupported: this.webglSupported
        };

        if (this.renderMode === '3d' && this.sceneManager3D) {
            return {
                ...baseStats,
                scene3D: this.sceneManager3D.getStats()
            };
        }

        return baseStats;
    }

    /**
     * 销毁渲染引擎
     */
    destroy() {
        if (this.canvas) {
            window.removeEventListener('resize', this.resize);
        }

        // 销毁3D场景管理器
        if (this.sceneManager3D) {
            this.sceneManager3D.destroy();
            this.sceneManager3D = null;
        }

        this.particleTrails.clear();
        this.particleCache.clear();
        this.effectCache.clear();

        console.log('🎨 渲染引擎已销毁');
    }
}

// 导出类到全局作用域
window.RenderEngine = RenderEngine;

// 创建全局渲染引擎实例
window.renderEngine = new RenderEngine();
