/**
 * 量子共鸣者 - 系统测试脚本
 * 用于测试各个系统模块的功能是否正常
 */

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 开始系统测试...');
    
    // 延迟执行测试，确保所有模块都已初始化
    setTimeout(() => {
        runSystemTests();
    }, 2000);
});

/**
 * 运行系统测试
 */
async function runSystemTests() {
    const testResults = {
        passed: 0,
        failed: 0,
        total: 0
    };

    console.log('='.repeat(50));
    console.log('🧪 量子共鸣者系统测试开始');
    console.log('='.repeat(50));

    // 测试存储服务
    await testStorageService(testResults);

    // 测试国际化服务
    await testI18nService(testResults);

    // 测试音频引擎
    await testAudioEngine(testResults);

    // 测试渲染引擎
    await testRenderEngine(testResults);

    // 测试量子引擎
    await testQuantumEngine(testResults);

    // 测试UI管理器
    await testUIManager(testResults);

    // 测试关卡管理器
    await testLevelManager(testResults);
    
    // 测试玩家管理器
    testPlayerManager(testResults);
    
    // 测试成就系统UI
    testAchievementsUI(testResults);
    
    // 测试排行榜UI
    testLeaderboardUI(testResults);
    
    // 测试游戏控制器
    testGameController(testResults);
    
    // 输出测试结果
    console.log('='.repeat(50));
    console.log('🧪 系统测试完成');
    console.log(`✅ 通过: ${testResults.passed}`);
    console.log(`❌ 失败: ${testResults.failed}`);
    console.log(`📊 总计: ${testResults.total}`);
    console.log(`🎯 成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    console.log('='.repeat(50));
    
    // 如果有失败的测试，显示警告
    if (testResults.failed > 0) {
        console.warn('⚠️ 部分系统测试失败，请检查上述错误信息');
    } else {
        console.log('🎉 所有系统测试通过！');
    }
}

/**
 * 测试辅助函数
 */
async function runTest(testName, testFunction, testResults) {
    testResults.total++;

    try {
        const result = await testFunction();
        if (result) {
            console.log(`✅ ${testName}: 通过`);
            testResults.passed++;
        } else {
            console.log(`❌ ${testName}: 失败`);
            testResults.failed++;
        }
    } catch (error) {
        console.log(`❌ ${testName}: 异常 - ${error.message}`);
        testResults.failed++;
    }
}

/**
 * 测试存储服务
 */
async function testStorageService(testResults) {
    console.log('\n📦 测试存储服务...');

    await runTest('存储服务初始化', () => {
        return window.storageService && typeof storageService.init === 'function';
    }, testResults);

    await runTest('存储服务基本操作', async () => {
        if (!window.storageService) return false;

        try {
            // 等待存储服务初始化完成
            await storageService.waitForInit();

            // 测试存储和读取
            const testKey = 'test.key';
            const testValue = { test: 'value', timestamp: Date.now() };

            await storageService.put(testKey, testValue);
            const retrieved = await storageService.get(testKey);

            return retrieved && retrieved.test === testValue.test;
        } catch (error) {
            console.error('存储服务测试失败:', error);
            return false;
        }
    }, testResults);
}

/**
 * 测试国际化服务
 */
async function testI18nService(testResults) {
    console.log('\n🌐 测试国际化服务...');

    await runTest('国际化服务初始化', () => {
        return window.i18n && typeof i18n.init === 'function';
    }, testResults);

    await runTest('语言切换功能', async () => {
        if (!window.i18n) return false;

        try {
            const originalLang = i18n.getCurrentLanguage();
            const newLang = originalLang === 'zh-CN' ? 'en-US' : 'zh-CN';

            await i18n.setLanguage(newLang);
            const currentLang = i18n.getCurrentLanguage();

            // 恢复原始语言
            await i18n.setLanguage(originalLang);

            return currentLang === newLang;
        } catch (error) {
            console.error('语言切换测试失败:', error);
            return false;
        }
    }, testResults);
}

/**
 * 测试音频引擎
 */
async function testAudioEngine(testResults) {
    console.log('\n🎵 测试音频引擎...');

    await runTest('音频引擎初始化', () => {
        return window.audioEngine && typeof audioEngine.init === 'function';
    }, testResults);

    await runTest('音频上下文创建', () => {
        return window.audioEngine && audioEngine.audioContext !== null;
    }, testResults);
}

/**
 * 测试渲染引擎
 */
async function testRenderEngine(testResults) {
    console.log('\n🎨 测试渲染引擎...');

    await runTest('渲染引擎初始化', () => {
        return window.renderEngine && typeof renderEngine.init === 'function';
    }, testResults);

    await runTest('Canvas元素获取', () => {
        return window.renderEngine && renderEngine.canvas !== null;
    }, testResults);
}

/**
 * 测试量子引擎
 */
async function testQuantumEngine(testResults) {
    console.log('\n⚛️ 测试量子引擎...');

    await runTest('量子引擎初始化', () => {
        return window.quantumEngine && typeof quantumEngine.init === 'function';
    }, testResults);

    await runTest('粒子系统创建', () => {
        return window.quantumEngine && Array.isArray(quantumEngine.particles);
    }, testResults);
}

/**
 * 测试UI管理器
 */
async function testUIManager(testResults) {
    console.log('\n🖥️ 测试UI管理器...');

    await runTest('UI管理器初始化', () => {
        return window.uiManager && typeof uiManager.init === 'function';
    }, testResults);

    await runTest('屏幕管理功能', () => {
        return window.uiManager && typeof uiManager.showScreen === 'function';
    }, testResults);
}

/**
 * 测试关卡管理器
 */
async function testLevelManager(testResults) {
    console.log('\n🎯 测试关卡管理器...');

    await runTest('关卡管理器初始化', () => {
        return window.levelManager && typeof levelManager.init === 'function';
    }, testResults);

    await runTest('内置关卡加载', () => {
        return window.levelManager && levelManager.builtInLevels && levelManager.builtInLevels.length > 0;
    }, testResults);
}

/**
 * 测试玩家管理器
 */
function testPlayerManager(testResults) {
    console.log('\n👤 测试玩家管理器...');
    
    runTest('玩家管理器初始化', () => {
        return window.playerManager && typeof playerManager.init === 'function';
    }, testResults);
    
    runTest('成就系统加载', () => {
        return window.playerManager && playerManager.achievements && playerManager.achievements.size > 0;
    }, testResults);
}

/**
 * 测试成就系统UI
 */
function testAchievementsUI(testResults) {
    console.log('\n🏅 测试成就系统UI...');
    
    runTest('成就UI初始化', () => {
        return window.achievementsUI && typeof achievementsUI.init === 'function';
    }, testResults);
    
    runTest('成就界面显示', () => {
        return window.achievementsUI && typeof achievementsUI.show === 'function';
    }, testResults);
}

/**
 * 测试排行榜UI
 */
function testLeaderboardUI(testResults) {
    console.log('\n🏆 测试排行榜UI...');
    
    runTest('排行榜UI初始化', () => {
        return window.leaderboardUI && typeof leaderboardUI.init === 'function';
    }, testResults);
    
    runTest('排行榜界面显示', () => {
        return window.leaderboardUI && typeof leaderboardUI.show === 'function';
    }, testResults);
}

/**
 * 测试游戏控制器
 */
function testGameController(testResults) {
    console.log('\n🎮 测试游戏控制器...');
    
    runTest('游戏控制器初始化', () => {
        return window.gameController && typeof gameController.init === 'function';
    }, testResults);
    
    runTest('游戏状态管理', () => {
        return window.gameController && typeof gameController.startGame === 'function';
    }, testResults);
}

// 添加到全局作用域以便在控制台中手动运行
window.runSystemTests = runSystemTests;
