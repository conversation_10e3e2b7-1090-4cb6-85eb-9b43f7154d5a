/**
 * 修复游戏背景显示问题的脚本
 * 确保背景样式正确应用和画布正确初始化
 */

class BackgroundFixer {
    constructor() {
        this.isFixed = false;
        console.log('🎨 背景修复器已初始化');
    }

    /**
     * 修复所有背景相关问题
     */
    fixAllBackgroundIssues() {
        console.log('🔍 开始检查和修复背景问题...');
        
        // 修复body背景样式
        this.fixBodyBackground();
        
        // 修复主菜单背景
        this.fixMainMenuBackground();
        
        // 初始化背景画布
        this.initBackgroundCanvas();
        
        // 验证修复结果
        this.verifyBackgroundFix();
        
        this.isFixed = true;
        console.log('✅ 背景修复完成');
    }

    /**
     * 修复body背景样式
     */
    fixBodyBackground() {
        const body = document.body;
        const computedStyle = window.getComputedStyle(body);
        
        console.log('🔍 检查body背景样式:', {
            background: computedStyle.background,
            backgroundColor: computedStyle.backgroundColor,
            backgroundImage: computedStyle.backgroundImage
        });

        // 确保CSS变量正确定义
        const rootStyle = window.getComputedStyle(document.documentElement);
        const bgGradient = rootStyle.getPropertyValue('--bg-gradient').trim();
        
        if (!bgGradient) {
            console.warn('⚠️ CSS变量 --bg-gradient 未定义，手动设置背景');
            body.style.background = 'linear-gradient(135deg, #0d1421 0%, #1a1a2e 50%, #16213e 100%)';
        } else {
            console.log('✅ CSS变量 --bg-gradient 已定义:', bgGradient);
            // 强制应用背景
            body.style.background = bgGradient;
        }

        // 确保其他必要样式
        body.style.margin = '0';
        body.style.padding = '0';
        body.style.minHeight = '100vh';
        body.style.overflow = 'hidden';
        
        console.log('✅ body背景样式已修复');
    }

    /**
     * 修复主菜单背景
     */
    fixMainMenuBackground() {
        const mainMenuScreen = document.getElementById('main-menu-screen');
        if (!mainMenuScreen) {
            console.warn('⚠️ 主菜单屏幕元素未找到');
            return;
        }

        const computedStyle = window.getComputedStyle(mainMenuScreen);
        console.log('🔍 检查主菜单背景:', {
            background: computedStyle.background,
            opacity: computedStyle.opacity,
            visibility: computedStyle.visibility,
            display: computedStyle.display
        });

        // 确保主菜单屏幕有正确的背景
        if (!computedStyle.background || computedStyle.background === 'rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box') {
            mainMenuScreen.style.background = 'var(--bg-gradient)';
            console.log('🔧 已设置主菜单背景');
        }

        console.log('✅ 主菜单背景已检查');
    }

    /**
     * 初始化背景画布
     */
    initBackgroundCanvas() {
        const canvas = document.getElementById('menu-background-canvas');
        if (!canvas) {
            console.warn('⚠️ 背景画布元素未找到');
            return;
        }

        console.log('🎨 初始化背景画布...');

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('❌ 无法获取画布上下文');
            return;
        }

        // 设置画布大小
        const resizeCanvas = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            console.log(`📐 画布大小设置为: ${canvas.width}x${canvas.height}`);
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 粒子系统
        const particles = [];
        const particleCount = 50;

        // 创建粒子
        const createParticle = () => {
            return {
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 2 + 1,
                opacity: Math.random() * 0.5 + 0.2,
                life: 1.0,
                decay: Math.random() * 0.01 + 0.005
            };
        };

        // 初始化粒子
        for (let i = 0; i < particleCount; i++) {
            particles.push(createParticle());
        }

        // 动画循环
        const animate = () => {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 更新和绘制粒子
            for (let i = particles.length - 1; i >= 0; i--) {
                const particle = particles[i];

                // 更新位置
                particle.x += particle.vx;
                particle.y += particle.vy;

                // 边界检查
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                // 更新生命值
                particle.life -= particle.decay;

                // 绘制粒子
                ctx.save();
                ctx.globalAlpha = particle.opacity * particle.life;
                ctx.fillStyle = '#6c5ce7';
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();

                // 移除死亡粒子
                if (particle.life <= 0) {
                    particles.splice(i, 1);
                    particles.push(createParticle());
                }
            }

            requestAnimationFrame(animate);
        };

        // 开始动画
        animate();

        console.log('✅ 背景画布初始化完成');
    }

    /**
     * 验证背景修复结果
     */
    verifyBackgroundFix() {
        console.log('🔍 验证背景修复结果...');

        const body = document.body;
        const bodyStyle = window.getComputedStyle(body);
        
        if (bodyStyle.backgroundImage && bodyStyle.backgroundImage !== 'none') {
            console.log('✅ body背景图像正常:', bodyStyle.backgroundImage);
        } else {
            console.warn('⚠️ body背景图像可能有问题');
        }

        const canvas = document.getElementById('menu-background-canvas');
        if (canvas && canvas.width > 0 && canvas.height > 0) {
            console.log('✅ 背景画布正常:', `${canvas.width}x${canvas.height}`);
        } else {
            console.warn('⚠️ 背景画布可能有问题');
        }

        console.log('✅ 背景验证完成');
    }
}

// 创建全局实例
window.backgroundFixer = new BackgroundFixer();

// 添加便捷方法
window.fixBackground = () => backgroundFixer.fixAllBackgroundIssues();

// 页面加载完成后自动修复
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🎨 自动修复背景问题...');
        backgroundFixer.fixAllBackgroundIssues();
    }, 1000);
});

console.log('🎨 背景修复器已加载');
console.log('💡 使用 fixBackground() 手动修复背景问题');
